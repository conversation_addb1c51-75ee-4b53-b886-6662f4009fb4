<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Application Form - Shifa Hospitals</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.18/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="Images/favicon.png" type="image/png">
    <style>
        :root {
            --primary-color: #2c5282;
            --secondary-color: #4299e1;
            --accent-color: #90cdf4;
            --text-color: #2d3748;
            --light-bg: #f7fafc;
            --success-color: #48bb78;
            --error-color: #e53e3e;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
            min-height: 100vh;
        }

        .main-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .form-header {
            text-align: center;
            margin-bottom: 35px;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        .form-header img {
            max-width: 180px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .form-header img:hover {
            transform: scale(1.05);
        }

        .form-header h2 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #718096;
            font-size: 1.1rem;
        }

        /* Language Toggle Button */
        .language-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #4a90e2;
            border: none;
            border-radius: 20px;
            padding: 6px 12px;
            color: white;
            font-weight: 500;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3);
            display: flex;
            align-items: center;
            gap: 6px;
            min-width: 80px;
            justify-content: center;
            height: 32px;
        }

        .language-toggle:hover {
            background: #357abd;
            box-shadow: 0 3px 8px rgba(74, 144, 226, 0.4);
        }

        .language-toggle:active {
            transform: scale(0.98);
        }

        .language-flag {
            width: 16px;
            height: 12px;
            border-radius: 2px;
            display: inline-block;
            background-size: cover;
            background-position: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .flag-en {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MDAiIHZpZXdCb3g9IjAgMCAxMjAwIDYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGRlZnM+CjxjbGlwUGF0aCBpZD0idCI+CjxwYXRoIGQ9Im0wLDBoMTIwMHY2MDBoLTEyMDB6Ii8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPGcgY2xpcC1wYXRoPSJ1cmwoI3QpIj4KPHBhdGggZD0ibTAsMGgxMjAwdjYwMGgtMTIwMHoiIGZpbGw9IiMwMTI0NjkiLz4KPHN0cm9rZSB3aWR0aD0iNDAiIHN0cm9rZT0iI2ZmZiI+CjxwYXRoIGQ9Im0wLDMwMGgxMjAwbTYwMCwwdi02MDBtMCw2MDB2NjAwIi8+CjwvZz4KPHN0cm9rZSB3aWR0aD0iMjQiIHN0cm9rZT0iI2NmMTQyYiI+CjxwYXRoIGQ9Im0wLDMwMGgxMjAwbTYwMCwwdi02MDBtMCw2MDB2NjAwIi8+CjwvZz4KPC9zdmc+');
        }

        .flag-ar {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MDAiIHZpZXdCb3g9IjAgMCAxMjAwIDYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyMDAiIGhlaWdodD0iNjAwIiBmaWxsPSIjZmZmIi8+CjxyZWN0IHdpZHRoPSIxMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2NlMTEyNiIvPgo8cmVjdCB5PSI0MDAiIHdpZHRoPSIxMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwNzIzOSIvPgo8cmVjdCB5PSIyMDAiIHdpZHRoPSIxMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwMCIvPgo8L3N2Zz4=');
        }

        /* RTL Support */
        .rtl {
            direction: rtl;
            text-align: right;
        }

        .rtl .form-header {
            text-align: center;
        }

        .rtl .language-toggle {
            right: auto;
            left: 20px;
        }

        .rtl .name-fields {
            direction: rtl;
        }

        .rtl .form-group label {
            text-align: right;
        }

        .rtl .form-group label i {
            margin-right: 0;
            margin-left: 8px;
        }

        .rtl .navigation-buttons {
            direction: rtl;
        }

        .rtl .navigation-buttons .button {
            direction: ltr;
        }

        /* Arabic Typography */
        .arabic-text {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica', sans-serif;
            font-weight: 400;
        }

        .rtl h1, .rtl h2, .rtl h3, .rtl h4, .rtl h5, .rtl h6 {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica', sans-serif;
            font-weight: 600;
        }

        .rtl .form-control, .rtl .name-field input {
            text-align: right;
            direction: rtl;
        }

        .rtl .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
            text-align: right;
            direction: rtl;
            padding-right: 12px;
            padding-left: 30px;
        }

        .rtl .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
            right: auto;
            left: 10px;
        }

        .rtl .required-field::after {
            margin-left: 0;
            margin-right: 4px;
        }

        /* RTL Popup Support */
        .rtl-popup {
            direction: rtl !important;
            text-align: right !important;
        }

        .rtl-popup .swal2-title {
            text-align: right !important;
        }

        .rtl-popup .swal2-content {
            text-align: right !important;
        }

        .rtl-popup .swal2-actions {
            direction: ltr !important;
        }

        /* Progress Indicator Styling */
        #svg_wrap {
            margin: 30px auto;
            text-align: center;
        }

        #svg_form_time {
            height: 24px;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            display: block;
        }

        #svg_form_time circle,
        #svg_form_time rect {
            fill: #e2e8f0;
            stroke: white;
            stroke-width: 2;
            transition: all 0.3s ease;
        }

        #svg_form_time circle:hover,
        #svg_form_time rect:hover {
            transform: scale(1.05);
        }

        /* Section Styling */
        section {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            padding: 35px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
            transform: translateX(100px);
            opacity: 0;
            display: none;
        }

        section.active {
            display: block;
            transform: translateX(0);
            opacity: 1;
        }

        section:hover {
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
        }

        section h4 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid var(--accent-color);
            text-align: center;
            position: relative;
        }

        section h4::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        /* Form Group Styling */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
            display: block;
        }

        .form-group label i {
            color: var(--primary-color);
            margin-right: 8px;
            width: 18px;
            text-align: center;
        }

        .form-control {
            height: calc(2.5em + 0.75rem + 2px);
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            transition: all 0.2s ease;
            padding: 12px 15px;
            background-color: #f8f9fa;
            font-size: 16px;
        }

        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
            border-color: var(--secondary-color);
            background-color: #fff;
            outline: none;
        }

        /* Name Fields Grid */
        .name-fields {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 1.5rem;
        }

        .name-field {
            position: relative;
        }

        .name-field label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 600;
        }

        .name-field input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
            font-size: 16px;
        }

        .name-field input:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
            background-color: #fff;
            outline: none;
        }

        /* Select2 Styling */
        .select2-container--bootstrap4 .select2-selection--single {
            height: calc(2.5em + 0.75rem + 2px) !important;
            padding: 0.375rem 0.75rem !important;
            border: 2px solid #e2e8f0 !important;
            border-radius: 8px !important;
            background-color: #f8f9fa !important;
            transition: all 0.3s ease !important;
            display: flex !important;
            align-items: center !important;
        }

        .select2-container--bootstrap4 .select2-selection--single:focus,
        .select2-container--bootstrap4.select2-container--open .select2-selection--single,
        .select2-container--bootstrap4.select2-container--focus .select2-selection--single {
            border-color: var(--secondary-color) !important;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2) !important;
            background-color: #fff !important;
            outline: none !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
            height: calc(2.5em + 0.75rem + 2px) !important;
            right: 10px !important;
            width: 20px !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow b {
            border-color: var(--text-color) transparent transparent transparent !important;
            border-style: solid !important;
            border-width: 5px 4px 0 4px !important;
            height: 0 !important;
            right: 90% !important;
            margin-left: -4px !important;
            margin-top: -2px !important;
            position: absolute !important;
            top: 50% !important;
            width: 0 !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
            line-height: calc(2.5em + 0.75rem) !important;
            color: var(--text-color) !important;
            padding-left: 12px !important;
            padding-right: 30px !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__placeholder {
            color: #6c757d !important;
        }

        /* Disabled Select2 Styling */
        .select2-container--bootstrap4.select2-container--disabled .select2-selection--single {
            background-color: #e9ecef !important;
            border-color: #ced4da !important;
            cursor: not-allowed !important;
            opacity: 0.65 !important;
        }

        .select2-container--bootstrap4.select2-container--disabled .select2-selection--single .select2-selection__rendered {
            color: #6c757d !important;
        }

        .select2-container--bootstrap4.select2-container--disabled .select2-selection--single .select2-selection__arrow b {
            border-color: #6c757d transparent transparent transparent !important;
        }

        /* Dropdown styling */
        .select2-container--bootstrap4 .select2-dropdown {
            border: 2px solid var(--secondary-color) !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }

        .select2-container--bootstrap4 .select2-results__option {
            padding: 8px 12px !important;
            transition: all 0.2s ease !important;
        }

        .select2-container--bootstrap4 .select2-results__option--highlighted {
            background-color: var(--secondary-color) !important;
            color: white !important;
        }

        /* Ensure proper width */
        .select2-container {
            width: 100% !important;
        }

        /* Navigation Buttons */
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
        }

        .button {
            background: var(--primary-color);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            color: white;
            cursor: pointer;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            font-size: 16px;
            min-width: 120px;
        }

        .button:hover:not(.disabled) {
            background-color: #1a365d;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(44, 82, 130, 0.3);
        }

        .button.disabled {
            background-color: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Validation Styling */
        .required-field::after {
            content: "*";
            color: var(--error-color);
            margin-left: 4px;
        }

        .is-valid {
            border-color: var(--success-color) !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2348bb78' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .is-invalid {
            border-color: var(--error-color) !important;
        }

        .invalid-feedback {
            display: none;
            color: var(--error-color);
            font-size: 0.875rem;
            margin-top: 6px;
        }

        .is-invalid ~ .invalid-feedback {
            display: block;
        }

        /* Animation Classes */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake {
            animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(229, 62, 62, 0.5); }
            70% { box-shadow: 0 0 0 10px rgba(229, 62, 62, 0); }
            100% { box-shadow: 0 0 0 0 rgba(229, 62, 62, 0); }
        }

        .pulse {
            animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* File Upload Styling */
        .custom-file {
            height: 48px;
        }

        .custom-file-input {
            height: 48px;
            cursor: pointer;
        }

        .custom-file-label {
            height: 48px !important;
            padding: 0.75rem 1rem;
            line-height: 1.5 !important;
            cursor: pointer;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }

        .custom-file-label:hover {
            border-color: var(--secondary-color);
            background-color: #fff;
        }

        .custom-file-label::after {
            height: 46px !important;
            padding: 0.75rem 1rem;
            line-height: 1.5 !important;
            background-color: #edf2f7;
            color: var(--text-color);
            border-radius: 0 8px 8px 0;
        }

        /* Submit Button Styling */
        .submit-container {
            text-align: center;
            margin-top: 30px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 12px 24px;
            font-weight: 600;
            border-radius: 8px;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            font-size: 16px;
            min-width: 200px;
        }

        .btn-primary:hover {
            background-color: #1a365d;
            border-color: #1a365d;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(44, 82, 130, 0.3);
        }

        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            display: none;
            vertical-align: text-bottom;
            margin-right: 8px;
            border-width: 0.2em;
        }

        .is-loading .spinner-border {
            display: inline-block;
        }

        /* Tooltip Styling */
        .custom-tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: var(--secondary-color);
            color: white;
            font-size: 12px;
            margin-left: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tooltip-icon:hover {
            background-color: var(--primary-color);
            transform: scale(1.1);
        }

        .custom-tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #2d3748;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.75rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .custom-tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #2d3748 transparent transparent transparent;
        }

        .custom-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        /* Helper Text */
        .helper-text {
            display: block;
            font-size: 0.8rem;
            color: #718096;
            margin-top: 0.25rem;
        }

        /* Loading States for Dropdowns */
        .form-control:disabled {
            background-color: #f8f9fa;
            opacity: 0.7;
            cursor: not-allowed;
        }

        .select2-container--disabled .select2-selection--single {
            background-color: #f8f9fa !important;
            cursor: not-allowed !important;
        }

        .loading-dropdown {
            position: relative;
        }

        .loading-dropdown::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 30px;
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 10;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }

            .form-header {
                padding: 20px 15px;
                margin-bottom: 20px;
            }

            .form-header img {
                max-width: 150px;
            }

            .form-header h2 {
                font-size: 1.5rem;
            }

            section {
                padding: 25px 20px;
            }

            .name-fields {
                grid-template-columns: 1fr;
            }

            .navigation-buttons {
                flex-direction: column;
                gap: 15px;
            }

            .button {
                width: 100%;
                min-width: auto;
            }

            .btn-primary {
                width: 100%;
                min-width: auto;
            }

            #svg_wrap {
                transform: scale(0.8);
                margin: 10px auto;
            }

            /* Fix input zooming on iOS */
            input[type="text"],
            input[type="email"],
            input[type="tel"],
            input[type="number"],
            select {
                font-size: 16px !important;
            }

            .form-control,
            .name-field input {
                height: 48px;
                min-height: 48px;
                font-size: 16px;
            }

            .select2-container--default .select2-selection--single {
                height: 48px;
            }

            .select2-container--default .select2-selection--single .select2-selection__rendered {
                line-height: 48px;
            }

            .select2-container--default .select2-selection--single .select2-selection__arrow {
                height: 46px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Form Header -->
        <div class="form-header">
            <!-- Language Toggle Button -->
            <button type="button" class="language-toggle" id="languageToggle">
                <span class="language-flag flag-en" id="languageFlag"></span>
                <span id="languageText">English</span>
            </button>

            <img src="Images/logo.png" alt="Shifa Hospitals Logo">
            <h2 data-en="Live Application Form" data-ar="نموذج طلب التوظيف المباشر">Live Application Form</h2>
            <p class="text-muted" data-en="Complete your application step by step" data-ar="أكمل طلبك خطوة بخطوة">Complete your application step by step</p>
        </div>

        <!-- Progress Indicator -->
        <div id="svg_wrap"></div>

        <!-- Multi-step Form -->
        <form id="liveApplicationForm" enctype="multipart/form-data">
            <!-- Hidden fields for tracking -->
            <input type="hidden" name="isLive" value="true">
            <input type="hidden" name="submissionSource" value="live-form">
            <input type="hidden" name="Application_Status" value="Live">

            <!-- Hidden fields for position hierarchy -->
            <input type="hidden" name="Main_Position" id="departmentId" value="1">
            <input type="hidden" name="SECTIONId" id="sectionId" value="1">

            <!-- Step 1: Basic Information -->
            <section>
                <h4 data-en="Basic Information" data-ar="المعلومات الأساسية"><i class="fas fa-user"></i> Basic Information</h4>

                <div class="name-fields">
                    <div class="name-field">
                        <label for="firstName" class="required-field" data-en="First Name" data-ar="الاسم الأول"><i class="fas fa-user"></i> First Name</label>
                        <input type="text" id="firstName" name="firstName" required
                               pattern="^([\u0600-\u06FF]+|[A-Za-z ]+)$"
                               title="Please enter a name using either Arabic or English characters (no mixing)"
                               data-placeholder-en="First Name" data-placeholder-ar="الاسم الأول">
                        <div class="invalid-feedback" data-en="Please enter your first name" data-ar="يرجى إدخال الاسم الأول">Please enter your first name</div>
                    </div>
                    <div class="name-field">
                        <label for="secondName" class="required-field" data-en="Second Name" data-ar="الاسم الثاني"><i class="fas fa-user"></i> Second Name</label>
                        <input type="text" id="secondName" name="secondName" required
                               pattern="^([\u0600-\u06FF]+|[A-Za-z ]+)$"
                               title="Please enter a name using either Arabic or English characters (no mixing)"
                               data-placeholder-en="Second Name" data-placeholder-ar="الاسم الثاني">
                        <div class="invalid-feedback" data-en="Please enter your second name" data-ar="يرجى إدخال الاسم الثاني">Please enter your second name</div>
                    </div>
                    <div class="name-field">
                        <label for="thirdName" class="required-field" data-en="Third Name" data-ar="الاسم الثالث"><i class="fas fa-user"></i> Third Name</label>
                        <input type="text" id="thirdName" name="thirdName" required
                               pattern="^([\u0600-\u06FF]+|[A-Za-z ]+)$"
                               title="Please enter a name using either Arabic or English characters (no mixing)"
                               data-placeholder-en="Third Name" data-placeholder-ar="الاسم الثالث">
                        <div class="invalid-feedback" data-en="Please enter your third name" data-ar="يرجى إدخال الاسم الثالث">Please enter your third name</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="phoneNumber" class="required-field" data-en="Phone Number" data-ar="رقم الهاتف"><i class="fas fa-phone"></i> Phone Number</label>
                            <input type="tel" class="form-control" id="phoneNumber" name="Mobile"
                                   pattern="[0-9]{11}" required
                                   data-placeholder-en="01xxxxxxxxx" data-placeholder-ar="01xxxxxxxxx">
                            <div class="invalid-feedback" data-en="Please enter a valid 11-digit phone number" data-ar="يرجى إدخال رقم هاتف صحيح مكون من 11 رقم">Please enter a valid 11-digit phone number</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" data-en="Email (Optional)" data-ar="البريد الإلكتروني (اختياري)"><i class="fas fa-envelope"></i> Email (Optional)</label>
                            <input type="email" class="form-control" id="email" name="Email"
                                   data-placeholder-en="Enter your email" data-placeholder-ar="أدخل بريدك الإلكتروني">
                            <div class="invalid-feedback" data-en="Please enter a valid email address" data-ar="يرجى إدخال عنوان بريد إلكتروني صحيح">Please enter a valid email address</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="idType" class="required-field" data-en="ID Type" data-ar="نوع الهوية"><i class="fas fa-id-card"></i> ID Type</label>
                            <select class="form-control" id="idType" name="ID_Type" required>
                                <option value="national" selected data-en="National ID" data-ar="الرقم القومي">National ID</option>
                                <option value="passport" data-en="Passport" data-ar="جواز السفر">Passport</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nationalId" id="idLabel" class="required-field" data-en="National ID" data-ar="الرقم القومي">
                                <i class="fas fa-id-card"></i> National ID
                                <span class="custom-tooltip">
                                    <span class="tooltip-icon">?</span>
                                    <span class="tooltip-text" id="idTooltip" data-en="Egyptian National ID should be 14 digits. Passport numbers vary by country." data-ar="الرقم القومي المصري يجب أن يكون 14 رقم. أرقام جوازات السفر تختلف حسب البلد.">Egyptian National ID should be 14 digits. Passport numbers vary by country.</span>
                                </span>
                            </label>
                            <input type="text" class="form-control" id="nationalId" name="National_ID" required
                                   data-placeholder-en="14 digits" data-placeholder-ar="14 رقم">
                            <div class="invalid-feedback" id="idFeedback" data-en="Please enter a valid 14-digit National ID" data-ar="يرجى إدخال رقم قومي صحيح مكون من 14 رقم">Please enter a valid 14-digit National ID</div>
                            <small class="helper-text" id="idHelper" data-en="For Egyptian citizens: 14 digits without spaces" data-ar="للمواطنين المصريين: 14 رقم بدون مسافات">For Egyptian citizens: 14 digits without spaces</small>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2: Personal Details -->
            <section>
                <h4 data-en="Personal Details" data-ar="البيانات الشخصية"><i class="fas fa-address-card"></i> Personal Details</h4>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="age" data-en="Age" data-ar="العمر"><i class="fas fa-birthday-cake"></i> Age</label>
                            <input type="text" class="form-control" id="age" name="Age" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="dateOfBirth" data-en="Birth Date" data-ar="تاريخ الميلاد"><i class="fas fa-calendar"></i> Birth Date</label>
                            <input type="text" class="form-control" id="dateOfBirth" name="Date_Of_Birth" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="governorate" data-en="Birth Place" data-ar="مكان الميلاد"><i class="fas fa-map-marker-alt"></i> Birth Place</label>
                            <input type="text" class="form-control" id="governorate" name="Birth_Place" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="gender" data-en="Gender" data-ar="الجنس"><i class="fas fa-venus-mars"></i> Gender</label>
                            <input type="text" class="form-control" id="gender" name="Gender" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="religion" class="required-field" data-en="Religion" data-ar="الديانة"><i class="fas fa-pray"></i> Religion</label>
                            <select class="form-control" id="religion" name="Religion" required>
                                <option value="" disabled selected data-en="Select Religion" data-ar="اختر الديانة">Select Religion</option>
                                <option value="Muslim" data-en="Muslim" data-ar="مسلم">Muslim</option>
                                <option value="Christian" data-en="Christian" data-ar="مسيحي">Christian</option>
                                <option value="Other" data-en="Other" data-ar="أخرى">Other</option>
                            </select>
                            <div class="invalid-feedback" data-en="Please select your religion" data-ar="يرجى اختيار الديانة">Please select your religion</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="socialStatus" class="required-field" data-en="Marital Status" data-ar="الحالة الاجتماعية"><i class="fas fa-heart"></i> Marital Status</label>
                            <select class="form-control" id="socialStatus" name="Marital_Status" required>
                                <option value="" disabled selected data-en="Select Marital Status" data-ar="اختر الحالة الاجتماعية">Select Marital Status</option>
                                <option value="Single" data-en="Single" data-ar="أعزب">Single</option>
                                <option value="Married" data-en="Married" data-ar="متزوج">Married</option>
                                <option value="Divorced" data-en="Divorced" data-ar="مطلق">Divorced</option>
                                <option value="Widowed" data-en="Widowed" data-ar="أرمل">Widowed</option>
                            </select>
                            <div class="invalid-feedback" data-en="Please select your marital status" data-ar="يرجى اختيار الحالة الاجتماعية">Please select your marital status</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="address" class="required-field" data-en="Address" data-ar="العنوان"><i class="fas fa-home"></i> Address</label>
                            <input type="text" class="form-control" id="address" name="Current_Address" required
                                   data-placeholder-en="Enter your address" data-placeholder-ar="أدخل عنوانك">
                            <div class="invalid-feedback" data-en="Please enter your address" data-ar="يرجى إدخال العنوان">Please enter your address</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="city" class="required-field" data-en="City" data-ar="المدينة"><i class="fas fa-city"></i> City</label>
                            <select class="form-control" id="city" name="City" required>
                                <option value="" disabled selected>Select City</option>
                                <option value="Cairo">Cairo</option>
                                <option value="Alexandria">Alexandria</option>
                                <option value="Port Said">Port Said</option>
                                <option value="Suez">Suez</option>
                                <option value="Damietta">Damietta</option>
                                <option value="Dakahlia">Dakahlia</option>
                                <option value="Sharqia">Sharqia</option>
                                <option value="Qalyubia">Qalyubia</option>
                                <option value="Kafr El Sheikh">Kafr El Sheikh</option>
                                <option value="Gharbia">Gharbia</option>
                                <option value="Monufia">Monufia</option>
                                <option value="Beheira">Beheira</option>
                                <option value="Ismailia">Ismailia</option>
                                <option value="Giza">Giza</option>
                                <option value="Beni Suef">Beni Suef</option>
                                <option value="Faiyum">Faiyum</option>
                                <option value="Minya">Minya</option>
                                <option value="Asyut">Asyut</option>
                                <option value="Sohag">Sohag</option>
                                <option value="Qena">Qena</option>
                                <option value="Aswan">Aswan</option>
                                <option value="Luxor">Luxor</option>
                                <option value="Red Sea">Red Sea</option>
                                <option value="New Valley">New Valley</option>
                                <option value="Matrouh">Matrouh</option>
                                <option value="North Sinai">North Sinai</option>
                                <option value="South Sinai">South Sinai</option>
                                <option value="Outside Republic">Outside Republic</option>
                            </select>
                            <div class="invalid-feedback">Please select your city</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="conscriptionStatus" class="required-field" data-en="Military Status" data-ar="الموقف من التجنيد"><i class="fas fa-shield-alt"></i> Military Status</label>
                    <select class="form-control" id="conscriptionStatus" name="Military_Status" required>
                        <option value="" disabled selected data-en="Select Military Status" data-ar="اختر الموقف من التجنيد">Select Military Status</option>
                        <option value="Completed" data-en="Completed" data-ar="مكتمل">Completed</option>
                        <option value="Exempted" data-en="Exempted" data-ar="معفى">Exempted</option>
                        <option value="Postponed" data-en="Postponed" data-ar="مؤجل">Postponed</option>
                        <option value="NotApplicable" data-en="Not Applicable" data-ar="غير مطبق">Not Applicable</option>
                    </select>
                    <div class="invalid-feedback" data-en="Please select your military status" data-ar="يرجى اختيار الموقف من التجنيد">Please select your military status</div>
                </div>
            </section>

            <!-- Step 3: Education -->
            <section>
                <h4 data-en="Education" data-ar="التعليم"><i class="fas fa-graduation-cap"></i> Education</h4>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="scientificDegree" class="required-field" data-en="Scientific Degree" data-ar="الدرجة العلمية"><i class="fas fa-award"></i> Scientific Degree</label>
                            <select class="form-control" id="scientificDegree" name="Scientific_Degree" required>
                                <option value="" disabled selected data-en="Select Scientific Degree" data-ar="اختر الدرجة العلمية">Select Scientific Degree</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                            <div class="invalid-feedback" data-en="Please select your scientific degree" data-ar="يرجى اختيار الدرجة العلمية">Please select your scientific degree</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="specialization" class="required-field" data-en="Specialization" data-ar="التخصص"><i class="fas fa-book"></i> Specialization</label>
                            <select class="form-control" id="specialization" name="Specialization" required disabled>
                                <option value="" disabled selected data-en="First select Scientific Degree" data-ar="اختر الدرجة العلمية أولاً">First select Scientific Degree</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                            <div class="invalid-feedback" data-en="Please select your specialization" data-ar="يرجى اختيار التخصص">Please select your specialization</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="university" data-en="University" data-ar="الجامعة"><i class="fas fa-university"></i> University</label>
                            <select class="form-control" id="university" name="University">
                                <option value="" disabled selected data-en="Select University" data-ar="اختر الجامعة">Select University</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="faculty" data-en="Faculty" data-ar="الكلية"><i class="fas fa-building"></i> Faculty</label>
                            <select class="form-control" id="faculty" name="Faculty">
                                <option value="" disabled selected data-en="Select Faculty" data-ar="اختر الكلية">Select Faculty</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="grade" class="required-field" data-en="Grade" data-ar="التقدير"><i class="fas fa-star"></i> Grade</label>
                            <select class="form-control" id="grade" name="Grade" required>
                                <option value="" disabled selected data-en="Select Grade" data-ar="اختر التقدير">Select Grade</option>
                                <!-- Options will be loaded dynamically from Qualifications table -->
                            </select>
                            <div class="invalid-feedback" data-en="Please select your grade" data-ar="يرجى اختيار التقدير">Please select your grade</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="yearOfGraduation" class="required-field" data-en="Year of Graduation" data-ar="سنة التخرج"><i class="fas fa-calendar-alt"></i> Year of Graduation</label>
                            <input type="text" class="form-control" id="yearOfGraduation" name="Grad_Year"
                                   pattern="\d{4}" maxlength="4" required
                                   data-placeholder-en="YYYY" data-placeholder-ar="السنة">
                            <div class="invalid-feedback" data-en="Please enter a valid graduation year" data-ar="يرجى إدخال سنة تخرج صحيحة">Please enter a valid graduation year</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 4: Experience -->
            <section>
                <h4 data-en="Experience" data-ar="الخبرة"><i class="fas fa-briefcase"></i> Experience</h4>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="currentEmployer" class="required-field" data-en="Current Employer" data-ar="جهة العمل الحالية"><i class="fas fa-building"></i> Current Employer</label>
                            <input type="text" class="form-control" id="currentEmployer" name="Current_Employer" required
                                   data-placeholder-en="Enter current employer" data-placeholder-ar="أدخل جهة العمل الحالية">
                            <div class="invalid-feedback" data-en="Please enter your current employer" data-ar="يرجى إدخال جهة العمل الحالية">Please enter your current employer</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="jobTitle" class="required-field" data-en="Job Title" data-ar="المسمى الوظيفي"><i class="fas fa-id-badge"></i> Job Title</label>
                            <input type="text" class="form-control" id="jobTitle" name="Job_Title" required
                                   data-placeholder-en="Enter job title" data-placeholder-ar="أدخل المسمى الوظيفي">
                            <div class="invalid-feedback" data-en="Please enter your job title" data-ar="يرجى إدخال المسمى الوظيفي">Please enter your job title</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="from" class="required-field" data-en="From Date" data-ar="تاريخ البداية"><i class="fas fa-calendar"></i> From Date</label>
                            <input type="date" class="form-control" id="from" name="EXP_Years_From" required>
                            <div class="invalid-feedback" data-en="Please enter the start date" data-ar="يرجى إدخال تاريخ البداية">Please enter the start date</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="to" class="required-field" data-en="To Date" data-ar="تاريخ النهاية"><i class="fas fa-calendar"></i> To Date</label>
                            <input type="date" class="form-control" id="to" name="EXP_Years_To" required>
                            <div class="invalid-feedback" data-en="Please enter the end date" data-ar="يرجى إدخال تاريخ النهاية">Please enter the end date</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="currentSalary" class="required-field" data-en="Current Salary" data-ar="الراتب الحالي"><i class="fas fa-money-bill-wave"></i> Current Salary</label>
                            <input type="text" class="form-control" id="currentSalary" name="Current_Salary"
                                   pattern="[0-9]*\.?[0-9]+" required
                                   data-placeholder-en="Enter current salary" data-placeholder-ar="أدخل الراتب الحالي">
                            <div class="invalid-feedback" data-en="Please enter your current salary" data-ar="يرجى إدخال الراتب الحالي">Please enter your current salary</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="reasonForLeaving" class="required-field" data-en="Reason for Leaving" data-ar="سبب ترك العمل"><i class="fas fa-comment"></i> Reason for Leaving</label>
                            <input type="text" class="form-control" id="reasonForLeaving" name="Leave_Reason" required
                                   data-placeholder-en="Enter reason for leaving" data-placeholder-ar="أدخل سبب ترك العمل">
                            <div class="invalid-feedback" data-en="Please enter the reason for leaving" data-ar="يرجى إدخال سبب ترك العمل">Please enter the reason for leaving</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="hearAboutUs" class="required-field" data-en="How Did You Hear About Us" data-ar="كيف سمعت عنا؟"><i class="fas fa-bullhorn"></i> How Did You Hear About Us</label>
                    <select class="form-control" id="hearAboutUs" name="Hear_About_Us" required>
                        <option value="" disabled selected data-en="How did you hear about us?" data-ar="كيف سمعت عنا؟">How did you hear about us?</option>
                        <option value="Wuzzuf" data-en="Wuzzuf" data-ar="وظف">Wuzzuf</option>
                        <option value="Facebook" data-en="Facebook" data-ar="فيسبوك">Facebook</option>
                        <option value="Forasna" data-en="Forasna" data-ar="فرصنا">Forasna</option>
                        <option value="Recommendation" data-en="Recommendation" data-ar="توصية">Recommendation</option>
                        <option value="Linkedin" data-en="Linkedin" data-ar="لينكد إن">Linkedin</option>
                    </select>
                    <div class="invalid-feedback" data-en="Please select how you heard about us" data-ar="يرجى اختيار كيف سمعت عنا">Please select how you heard about us</div>
                </div>
            </section>

            <!-- Step 5: Position & Additional Info -->
            <section>
                <h4 data-en="Position & Additional Information" data-ar="المنصب والمعلومات الإضافية"><i class="fas fa-user-tie"></i> Position & Additional Information</h4>

                <div class="form-group">
                    <label for="position" class="required-field" data-en="Position Interested In" data-ar="المنصب المهتم به"><i class="fas fa-briefcase"></i> Position Interested In</label>
                    <select class="form-control" id="position" name="Sub_Position" required>
                        <option value="" data-en="Select Position" data-ar="اختر المنصب">Select Position</option>
                        <!-- Positions will be loaded dynamically -->
                    </select>
                    <div class="invalid-feedback" data-en="Please select a position" data-ar="يرجى اختيار المنصب">Please select a position</div>
                </div>

                <div class="form-group">
                    <label for="expectedSalary" class="required-field" data-en="Expected Salary" data-ar="الراتب المتوقع"><i class="fas fa-money-bill-wave"></i> Expected Salary</label>
                    <input type="text" class="form-control" id="expectedSalary" name="Expected_Salary"
                           pattern="[0-9]*\.?[0-9]+" required
                           data-placeholder-en="Enter expected salary" data-placeholder-ar="أدخل الراتب المتوقع">
                    <div class="invalid-feedback" data-en="Please enter your expected salary" data-ar="يرجى إدخال الراتب المتوقع">Please enter your expected salary</div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="englishLevel" class="required-field" data-en="English Level" data-ar="مستوى الإنجليزية"><i class="fas fa-language"></i> English Level</label>
                            <select class="form-control" id="englishLevel" name="EMP_ENG_LVL" required>
                                <option value="" disabled selected data-en="Select English Level" data-ar="اختر مستوى الإنجليزية">Select English Level</option>
                                <option value="Fluent" data-en="Fluent" data-ar="ممتاز">Fluent</option>
                                <option value="Very Good" data-en="Very Good" data-ar="جيد جداً">Very Good</option>
                                <option value="Good" data-en="Good" data-ar="جيد">Good</option>
                                <option value="Poor" data-en="Poor" data-ar="ضعيف">Poor</option>
                            </select>
                            <div class="invalid-feedback" data-en="Please select your English level" data-ar="يرجى اختيار مستوى الإنجليزية">Please select your English level</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="computerSkills" class="required-field" data-en="Computer Skills Level" data-ar="مستوى مهارات الحاسوب"><i class="fas fa-laptop"></i> Computer Skills Level</label>
                            <select class="form-control" id="computerSkills" name="Computer_Skills" required>
                                <option value="" disabled selected data-en="Select Computer Skills Level" data-ar="اختر مستوى مهارات الحاسوب">Select Computer Skills Level</option>
                                <option value="Excellent" data-en="Excellent" data-ar="ممتاز">Excellent</option>
                                <option value="Very Good" data-en="Very Good" data-ar="جيد جداً">Very Good</option>
                                <option value="Good" data-en="Good" data-ar="جيد">Good</option>
                                <option value="Poor" data-en="Poor" data-ar="ضعيف">Poor</option>
                            </select>
                            <div class="invalid-feedback" data-en="Please select your computer skills level" data-ar="يرجى اختيار مستوى مهارات الحاسوب">Please select your computer skills level</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="workAtShifa" class="required-field" data-en="Previous Work at Shifa Hospital" data-ar="العمل السابق في مستشفى الشفاء"><i class="fas fa-hospital"></i> Previous Work at Shifa Hospital</label>
                            <select class="form-control" id="workAtShifa" name="Work_At_Shifa" required>
                                <option value="" disabled selected data-en="Do you work at Shifa Hospital Before?" data-ar="هل عملت في مستشفى الشفاء من قبل؟">Do you work at Shifa Hospital Before?</option>
                                <option value="Yes" data-en="Yes" data-ar="نعم">Yes</option>
                                <option value="No" data-en="No" data-ar="لا">No</option>
                            </select>
                            <div class="invalid-feedback" data-en="Please select if you worked at Shifa before" data-ar="يرجى اختيار ما إذا كنت عملت في الشفاء من قبل">Please select if you worked at Shifa before</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="chronicDiseases" class="required-field" data-en="Chronic Diseases" data-ar="الأمراض المزمنة"><i class="fas fa-heartbeat"></i> Chronic Diseases</label>
                            <select class="form-control" id="chronicDiseases" name="Chronic_Diseases" required>
                                <option value="" disabled selected data-en="Do you have any chronic diseases?" data-ar="هل تعاني من أي أمراض مزمنة؟">Do you have any chronic diseases?</option>
                                <option value="Yes" data-en="Yes" data-ar="نعم">Yes</option>
                                <option value="No" data-en="No" data-ar="لا">No</option>
                            </select>
                            <div class="invalid-feedback" data-en="Please select if you have chronic diseases" data-ar="يرجى اختيار ما إذا كنت تعاني من أمراض مزمنة">Please select if you have chronic diseases</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="relativeInHospital" class="required-field" data-en="Relatives in Hospital" data-ar="أقارب في المستشفى"><i class="fas fa-users"></i> Relatives in Hospital</label>
                    <select class="form-control" id="relativeInHospital" name="Relative_In_Hospital" required>
                        <option value="" disabled selected data-en="Do you have Relatives in the Hospital?" data-ar="هل لديك أقارب في المستشفى؟">Do you have Relatives in the Hospital?</option>
                        <option value="Yes" data-en="Yes" data-ar="نعم">Yes</option>
                        <option value="No" data-en="No" data-ar="لا">No</option>
                    </select>
                    <div class="invalid-feedback" data-en="Please select if you have relatives in the hospital" data-ar="يرجى اختيار ما إذا كان لديك أقارب في المستشفى">Please select if you have relatives in the hospital</div>
                </div>

                <div class="form-group">
                    <label for="cvFile" data-en="Upload CV (Optional)" data-ar="رفع السيرة الذاتية (اختياري)"><i class="fas fa-file-upload"></i> Upload CV (Optional)</label>
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="cvFile" name="cvFile" accept=".pdf,.doc,.docx">
                        <label class="custom-file-label" for="cvFile" data-en="Choose file" data-ar="اختر ملف">Choose file</label>
                    </div>
                    <small class="form-text text-muted" data-en="Accepted formats: PDF, DOC, DOCX (Max 5MB)" data-ar="الصيغ المقبولة: PDF, DOC, DOCX (حد أقصى 5 ميجابايت)">Accepted formats: PDF, DOC, DOCX (Max 5MB)</small>
                </div>
            </section>

            <!-- Step 6: Review & Submit -->
            <section>
                <h4 data-en="Review & Submit" data-ar="المراجعة والإرسال"><i class="fas fa-check-circle"></i> Review & Submit</h4>

                <div class="submit-container">
                    <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                        <span class="spinner-border" role="status" aria-hidden="true"></span>
                        <i class="fas fa-paper-plane"></i> Submit Application
                    </button>
                </div>
            </section>
        </form>

        <!-- Navigation Buttons -->
        <div class="navigation-buttons">
            <button class="button" id="prev"><i class="fas fa-arrow-left"></i> Previous</button>
            <button class="button" id="next">Next <i class="fas fa-arrow-right"></i></button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.0.18/dist/sweetalert2.min.js"></script>

    <script>
        $(document).ready(function() {
            // Multi-step form variables
            var child = 1;
            var length = $("section").length;
            var base_color = '#e2e8f0';
            var active_color = '#2c5282';

            // Language variables
            var currentLanguage = localStorage.getItem('formLanguage') || 'en';

            // Global data caches for ID mapping
            var universitiesData = [];
            var scientificDegreesData = [];
            var facultiesData = [];
            var specializationsData = [];
            var gradesData = []; // Cache for all grades from Qualifications table

            // Initialize form
            initializeLanguage();
            initializeForm();
            initializeSelect2();
            loadPositions();
            loadScientificDegrees();
            loadUniversities();
            loadFaculties();
            loadGrades(); // Load grades from Qualifications table
            setupValidation();
            setupNavigation();
            setupCascadingDropdowns();
            setupLanguageToggle();

            // Load data for ID mapping
            loadDataForIdMapping();

            // Load data for ID mapping functionality
            async function loadDataForIdMapping() {
                try {
                    console.log('🔄 Loading data for ID mapping...');

                    // Load universities data
                    const universitiesResponse = await fetch('/api/universities');
                    if (universitiesResponse.ok) {
                        universitiesData = await universitiesResponse.json();
                        console.log(`✅ Loaded ${universitiesData.length} universities for ID mapping`);
                    }

                    // Load scientific degrees data
                    const scientificDegreesResponse = await fetch('/api/scientific-degrees');
                    if (scientificDegreesResponse.ok) {
                        scientificDegreesData = await scientificDegreesResponse.json();
                        console.log(`✅ Loaded ${scientificDegreesData.length} scientific degrees for ID mapping`);
                    }

                    // Load faculties data
                    const facultiesResponse = await fetch('/api/faculties');
                    if (facultiesResponse.ok) {
                        facultiesData = await facultiesResponse.json();
                        console.log(`✅ Loaded ${facultiesData.length} faculties for ID mapping`);
                    }

                    // Load grades data from Qualifications table
                    const gradesResponse = await fetch('/api/qualifications');
                    if (gradesResponse.ok) {
                        gradesData = await gradesResponse.json();
                        console.log(`✅ Loaded ${gradesData.length} grades for ID mapping`);
                    }

                    console.log('🎉 ID mapping data loaded successfully');
                } catch (error) {
                    console.error('❌ Error loading data for ID mapping:', error);
                }
            }

            // ID Mapping Functions
            function findUniversityIdByName(displayName) {
                if (!displayName || !universitiesData.length) return null;

                const university = universitiesData.find(uni =>
                    uni.uniname_en === displayName ||
                    uni.uniname_ar === displayName ||
                    (uni.uniname_en && uni.uniname_en.toLowerCase() === displayName.toLowerCase()) ||
                    (uni.uniname_ar && uni.uniname_ar === displayName)
                );

                return university ? university.uni_id : null;
            }

            function findScientificDegreeIdByName(displayName) {
                if (!displayName || !scientificDegreesData.length) return null;

                const degree = scientificDegreesData.find(deg =>
                    deg.CategoryName_EN === displayName ||
                    deg.CategoryName_AR === displayName ||
                    (deg.CategoryName_EN && deg.CategoryName_EN.toLowerCase() === displayName.toLowerCase()) ||
                    (deg.CategoryName_AR && deg.CategoryName_AR === displayName)
                );

                return degree ? degree.Category_ID : null;
            }

            function findFacultyIdByName(displayName) {
                if (!displayName || !facultiesData.length) return null;

                const faculty = facultiesData.find(fac =>
                    fac.facname_en === displayName ||
                    fac.facname_ar === displayName ||
                    (fac.facname_en && fac.facname_en.toLowerCase() === displayName.toLowerCase()) ||
                    (fac.facname_ar && fac.facname_ar === displayName)
                );

                return faculty ? faculty.fac_id : null;
            }

            function findGradeIdByName(displayName, categoryId) {
                if (!displayName || !gradesData.length) return null;

                // Find grade in cached data
                const grade = gradesData.find(g =>
                    (g.GradeName_EN === displayName ||
                     g.GradeName_AR === displayName ||
                     (g.GradeName_EN && g.GradeName_EN.toLowerCase() === displayName.toLowerCase()) ||
                     (g.GradeName_AR && g.GradeName_AR === displayName)) &&
                    (!categoryId || g.Category_ID == categoryId) // Optional category filter
                );

                return grade ? grade.Grade_ID : null;
            }

            function findGradeArNameByEnName(displayName, categoryId) {
                if (!displayName || !gradesData.length) return displayName;

                // Find grade in cached data and return Arabic name
                const grade = gradesData.find(g =>
                    (g.GradeName_EN === displayName ||
                     (g.GradeName_EN && g.GradeName_EN.toLowerCase() === displayName.toLowerCase())) &&
                    (!categoryId || g.Category_ID == categoryId) // Optional category filter
                );

                return grade ? grade.GradeName_AR : displayName;
            }

            // Debug function to check Select2 status
            window.debugSelect2 = function() {
                console.log('=== Select2 Debug Info ===');
                console.log('Specialization field exists:', $('#specialization').length > 0);
                console.log('Specialization has Select2:', $('#specialization').hasClass('select2-hidden-accessible'));
                console.log('Specialization is disabled:', $('#specialization').prop('disabled'));
                console.log('Specialization value:', $('#specialization').val());
                console.log('Specialization options:', $('#specialization option').length);
                console.log('Select2 containers:', $('.select2-container').length);
                console.log('=========================');
            };

            // Debug function to test ID mapping
            window.debugIdMapping = function() {
                console.log('=== ID Mapping Debug ===');
                console.log('Universities data:', universitiesData.length);
                console.log('Scientific degrees data:', scientificDegreesData.length);
                console.log('Faculties data:', facultiesData.length);

                // Test mappings
                const testUniversity = universitiesData[0];
                if (testUniversity) {
                    console.log('Test university mapping:', testUniversity.uniname_en, '->', findUniversityIdByName(testUniversity.uniname_en));
                }

                const testDegree = scientificDegreesData[0];
                if (testDegree) {
                    console.log('Test degree mapping:', testDegree.CategoryName_EN, '->', findScientificDegreeIdByName(testDegree.CategoryName_EN));
                }

                const testFaculty = facultiesData[0];
                if (testFaculty) {
                    console.log('Test faculty mapping:', testFaculty.facname_en, '->', findFacultyIdByName(testFaculty.facname_en));
                }
                console.log('========================');
            };

            // Language Management Functions
            function initializeLanguage() {
                // Set initial language
                setLanguage(currentLanguage);
            }

            function setupLanguageToggle() {
                $('#languageToggle').on('click', function() {
                    currentLanguage = currentLanguage === 'en' ? 'ar' : 'en';
                    localStorage.setItem('formLanguage', currentLanguage);
                    setLanguage(currentLanguage);
                });
            }

            function setLanguage(lang) {
                currentLanguage = lang;

                // Update body class for RTL/LTR
                if (lang === 'ar') {
                    $('body').addClass('rtl').removeClass('ltr');
                    $('html').attr('dir', 'rtl').attr('lang', 'ar');
                } else {
                    $('body').addClass('ltr').removeClass('rtl');
                    $('html').attr('dir', 'ltr').attr('lang', 'en');
                }

                // Update language toggle button
                updateLanguageToggle(lang);

                // Update all text content
                updateTextContent(lang);

                // Update form placeholders
                updatePlaceholders(lang);

                // Update dropdown options
                updateDropdownOptions(lang);

                // Update dynamic dropdown placeholders
                updateDynamicDropdownPlaceholders(lang);

                // Reinitialize Select2 for RTL support
                reinitializeSelect2ForLanguage(lang);
            }

            function updateLanguageToggle(lang) {
                const toggle = $('#languageToggle');
                const flag = $('#languageFlag');
                const text = $('#languageText');

                if (lang === 'ar') {
                    flag.removeClass('flag-en').addClass('flag-ar');
                    text.text('العربية');
                } else {
                    flag.removeClass('flag-ar').addClass('flag-en');
                    text.text('English');
                }
            }

            function updateTextContent(lang) {
                // Update all elements with data-en and data-ar attributes
                $('[data-en][data-ar]').each(function() {
                    const element = $(this);
                    const text = element.attr('data-' + lang);
                    if (text) {
                        if (element.is('option')) {
                            // For option elements, just update text content
                            element.text(text);
                        } else {
                            // Preserve icons in labels and headings
                            const icon = element.find('i').length > 0 ? element.find('i')[0].outerHTML + ' ' : '';
                            element.html(icon + text);
                        }
                    }
                });

                // Update navigation buttons
                updateNavigationButtons(lang);

                // Update submit button
                updateSubmitButton(lang);
            }

            function updatePlaceholders(lang) {
                // Update input placeholders
                $('[data-placeholder-en][data-placeholder-ar]').each(function() {
                    const element = $(this);
                    const placeholder = element.attr('data-placeholder-' + lang);
                    if (placeholder) {
                        element.attr('placeholder', placeholder);
                    }
                });
            }

            function updateNavigationButtons(lang) {
                if (lang === 'ar') {
                    $('#prev').html('<i class="fas fa-arrow-right"></i> السابق');
                    $('#next').html('التالي <i class="fas fa-arrow-left"></i>');
                } else {
                    $('#prev').html('<i class="fas fa-arrow-left"></i> Previous');
                    $('#next').html('Next <i class="fas fa-arrow-right"></i>');
                }
            }

            function updateSubmitButton(lang) {
                const submitBtn = $('#submitBtn');
                if (lang === 'ar') {
                    submitBtn.html('<span class="spinner-border" role="status" aria-hidden="true"></span><i class="fas fa-paper-plane"></i> إرسال الطلب');
                } else {
                    submitBtn.html('<span class="spinner-border" role="status" aria-hidden="true"></span><i class="fas fa-paper-plane"></i> Submit Application');
                }
            }

            function updateDropdownOptions(lang) {
                // Update static dropdown options
                updateStaticDropdowns(lang);
            }

            function updateStaticDropdowns(lang) {
                // Religion dropdown
                const religionOptions = {
                    en: [
                        { value: '', text: 'Select Religion' },
                        { value: 'Muslim', text: 'Muslim' },
                        { value: 'Christian', text: 'Christian' },
                        { value: 'Other', text: 'Other' }
                    ],
                    ar: [
                        { value: '', text: 'اختر الديانة' },
                        { value: 'Muslim', text: 'مسلم' },
                        { value: 'Christian', text: 'مسيحي' },
                        { value: 'Other', text: 'أخرى' }
                    ]
                };

                // Marital Status dropdown
                const maritalOptions = {
                    en: [
                        { value: '', text: 'Select Marital Status' },
                        { value: 'Single', text: 'Single' },
                        { value: 'Married', text: 'Married' },
                        { value: 'Divorced', text: 'Divorced' },
                        { value: 'Widowed', text: 'Widowed' }
                    ],
                    ar: [
                        { value: '', text: 'اختر الحالة الاجتماعية' },
                        { value: 'Single', text: 'أعزب' },
                        { value: 'Married', text: 'متزوج' },
                        { value: 'Divorced', text: 'مطلق' },
                        { value: 'Widowed', text: 'أرمل' }
                    ]
                };

                // Update dropdowns
                updateDropdown('#religion', religionOptions[lang]);
                updateDropdown('#socialStatus', maritalOptions[lang]);

                // Add more static dropdowns as needed
                updateCityDropdown(lang);
                updateMilitaryStatusDropdown(lang);
                updateEnglishLevelDropdown(lang);
                updateComputerSkillsDropdown(lang);
                updateGradeDropdown(lang);
                updateHearAboutUsDropdown(lang);
                updateYesNoDropdowns(lang);
            }

            function updateDropdown(selector, options) {
                const dropdown = $(selector);
                const currentValue = dropdown.val();

                // Destroy Select2 if exists
                if (dropdown.hasClass('select2-hidden-accessible')) {
                    dropdown.select2('destroy');
                }

                // Clear and populate options
                dropdown.empty();
                options.forEach(function(option) {
                    dropdown.append(`<option value="${option.value}" ${option.value === currentValue ? 'selected' : ''}>${option.text}</option>`);
                });

                // Reinitialize Select2
                dropdown.select2({
                    theme: 'bootstrap4',
                    placeholder: options[0].text,
                    allowClear: false
                });
            }

            function reinitializeSelect2ForLanguage(lang) {
                // Reinitialize all Select2 dropdowns for RTL support
                $('.select2-hidden-accessible').each(function() {
                    const element = $(this);
                    const currentValue = element.val();

                    element.select2('destroy');
                    element.select2({
                        theme: 'bootstrap4',
                        placeholder: element.find('option:first').text(),
                        allowClear: false,
                        dir: lang === 'ar' ? 'rtl' : 'ltr'
                    });

                    if (currentValue) {
                        element.val(currentValue).trigger('change');
                    }
                });
            }

            function updateDynamicDropdownPlaceholders(lang) {
                // Update Scientific Degree placeholder
                const scientificDegreeText = lang === 'ar' ? 'اختر الدرجة العلمية' : 'Select Scientific Degree';
                if ($('#scientificDegree option:first').length > 0) {
                    $('#scientificDegree option:first').text(scientificDegreeText);
                }

                // Update Specialization placeholder
                const specializationText = lang === 'ar' ? 'اختر الدرجة العلمية أولاً' : 'First select Scientific Degree';
                if ($('#specialization option:first').length > 0) {
                    $('#specialization option:first').text(specializationText);
                }

                // Update University placeholder
                const universityText = lang === 'ar' ? 'اختر الجامعة' : 'Select University';
                if ($('#university option:first').length > 0) {
                    $('#university option:first').text(universityText);
                }

                // Update Faculty placeholder
                const facultyText = lang === 'ar' ? 'اختر الكلية' : 'Select Faculty';
                if ($('#faculty option:first').length > 0) {
                    $('#faculty option:first').text(facultyText);
                }

                // Update Position placeholder
                const positionText = lang === 'ar' ? 'اختر المنصب' : 'Select Position';
                if ($('#position option:first').length > 0) {
                    $('#position option:first').text(positionText);
                }

                // Update loading and error messages for specializations
                if ($('#specialization option').length === 1) {
                    const loadingText = lang === 'ar' ? 'جاري التحميل...' : 'Loading...';
                    if ($('#specialization option:first').text().includes('Loading') || $('#specialization option:first').text().includes('جاري')) {
                        $('#specialization option:first').text(loadingText);
                    }
                }

                // Update file upload text
                const fileText = lang === 'ar' ? 'اختر ملف' : 'Choose file';
                $('.custom-file-label').text(fileText);
            }

            // Helper functions for specific dropdowns
            function updateCityDropdown(lang) {
                const cityOptions = {
                    en: [
                        { value: '', text: 'Select City' },
                        { value: 'Cairo', text: 'Cairo' },
                        { value: 'Alexandria', text: 'Alexandria' },
                        { value: 'Port Said', text: 'Port Said' },
                        { value: 'Suez', text: 'Suez' },
                        { value: 'Damietta', text: 'Damietta' },
                        { value: 'Dakahlia', text: 'Dakahlia' },
                        { value: 'Sharqia', text: 'Sharqia' },
                        { value: 'Qalyubia', text: 'Qalyubia' },
                        { value: 'Kafr El Sheikh', text: 'Kafr El Sheikh' },
                        { value: 'Gharbia', text: 'Gharbia' },
                        { value: 'Monufia', text: 'Monufia' },
                        { value: 'Beheira', text: 'Beheira' },
                        { value: 'Ismailia', text: 'Ismailia' },
                        { value: 'Giza', text: 'Giza' },
                        { value: 'Beni Suef', text: 'Beni Suef' },
                        { value: 'Faiyum', text: 'Faiyum' },
                        { value: 'Minya', text: 'Minya' },
                        { value: 'Asyut', text: 'Asyut' },
                        { value: 'Sohag', text: 'Sohag' },
                        { value: 'Qena', text: 'Qena' },
                        { value: 'Aswan', text: 'Aswan' },
                        { value: 'Luxor', text: 'Luxor' },
                        { value: 'Red Sea', text: 'Red Sea' },
                        { value: 'New Valley', text: 'New Valley' },
                        { value: 'Matrouh', text: 'Matrouh' },
                        { value: 'North Sinai', text: 'North Sinai' },
                        { value: 'South Sinai', text: 'South Sinai' },
                        { value: 'Outside Republic', text: 'Outside Republic' }
                    ],
                    ar: [
                        { value: '', text: 'اختر المدينة' },
                        { value: 'Cairo', text: 'القاهرة' },
                        { value: 'Alexandria', text: 'الإسكندرية' },
                        { value: 'Port Said', text: 'بورسعيد' },
                        { value: 'Suez', text: 'السويس' },
                        { value: 'Damietta', text: 'دمياط' },
                        { value: 'Dakahlia', text: 'الدقهلية' },
                        { value: 'Sharqia', text: 'الشرقية' },
                        { value: 'Qalyubia', text: 'القليوبية' },
                        { value: 'Kafr El Sheikh', text: 'كفر الشيخ' },
                        { value: 'Gharbia', text: 'الغربية' },
                        { value: 'Monufia', text: 'المنوفية' },
                        { value: 'Beheira', text: 'البحيرة' },
                        { value: 'Ismailia', text: 'الإسماعيلية' },
                        { value: 'Giza', text: 'الجيزة' },
                        { value: 'Beni Suef', text: 'بني سويف' },
                        { value: 'Faiyum', text: 'الفيوم' },
                        { value: 'Minya', text: 'المنيا' },
                        { value: 'Asyut', text: 'أسيوط' },
                        { value: 'Sohag', text: 'سوهاج' },
                        { value: 'Qena', text: 'قنا' },
                        { value: 'Aswan', text: 'أسوان' },
                        { value: 'Luxor', text: 'الأقصر' },
                        { value: 'Red Sea', text: 'البحر الأحمر' },
                        { value: 'New Valley', text: 'الوادي الجديد' },
                        { value: 'Matrouh', text: 'مطروح' },
                        { value: 'North Sinai', text: 'شمال سيناء' },
                        { value: 'South Sinai', text: 'جنوب سيناء' },
                        { value: 'Outside Republic', text: 'خارج الجمهورية' }
                    ]
                };
                updateDropdown('#city', cityOptions[lang]);
            }

            function updateMilitaryStatusDropdown(lang) {
                const militaryOptions = {
                    en: [
                        { value: '', text: 'Select Military Status' },
                        { value: 'Completed', text: 'Completed' },
                        { value: 'Exempted', text: 'Exempted' },
                        { value: 'Postponed', text: 'Postponed' },
                        { value: 'NotApplicable', text: 'Not Applicable' }
                    ],
                    ar: [
                        { value: '', text: 'اختر الموقف من التجنيد' },
                        { value: 'Completed', text: 'مكتمل' },
                        { value: 'Exempted', text: 'معفى' },
                        { value: 'Postponed', text: 'مؤجل' },
                        { value: 'NotApplicable', text: 'غير مطبق' }
                    ]
                };
                updateDropdown('#conscriptionStatus', militaryOptions[lang]);
            }

            function updateEnglishLevelDropdown(lang) {
                const englishOptions = {
                    en: [
                        { value: '', text: 'Select English Level' },
                        { value: 'Fluent', text: 'Fluent' },
                        { value: 'Very Good', text: 'Very Good' },
                        { value: 'Good', text: 'Good' },
                        { value: 'Poor', text: 'Poor' }
                    ],
                    ar: [
                        { value: '', text: 'اختر مستوى الإنجليزية' },
                        { value: 'Fluent', text: 'ممتاز' },
                        { value: 'Very Good', text: 'جيد جداً' },
                        { value: 'Good', text: 'جيد' },
                        { value: 'Poor', text: 'ضعيف' }
                    ]
                };
                updateDropdown('#englishLevel', englishOptions[lang]);
            }

            function updateComputerSkillsDropdown(lang) {
                const computerOptions = {
                    en: [
                        { value: '', text: 'Select Computer Skills Level' },
                        { value: 'Excellent', text: 'Excellent' },
                        { value: 'Very Good', text: 'Very Good' },
                        { value: 'Good', text: 'Good' },
                        { value: 'Poor', text: 'Poor' }
                    ],
                    ar: [
                        { value: '', text: 'اختر مستوى مهارات الحاسوب' },
                        { value: 'Excellent', text: 'ممتاز' },
                        { value: 'Very Good', text: 'جيد جداً' },
                        { value: 'Good', text: 'جيد' },
                        { value: 'Poor', text: 'ضعيف' }
                    ]
                };
                updateDropdown('#computerSkills', computerOptions[lang]);
            }

            function updateGradeDropdown(lang) {
                const gradeSelect = $('#grade');
                const currentValue = gradeSelect.val();

                // Clear and rebuild options from cached data
                const selectText = lang === 'ar' ? 'اختر التقدير' : 'Select Grade';
                gradeSelect.empty().append(`<option value="" disabled selected>${selectText}</option>`);

                if (gradesData && gradesData.length > 0) {
                    // Get unique grades
                    const uniqueGrades = [];
                    const seenGrades = new Set();

                    gradesData.forEach(function(qual) {
                        if (qual.Grade_ID && qual.GradeName_EN && !seenGrades.has(qual.Grade_ID)) {
                            seenGrades.add(qual.Grade_ID);
                            uniqueGrades.push(qual);
                        }
                    });

                    // Sort grades by logical order
                    const gradeOrder = ['Excellent', 'Very Good', 'Good', 'Pass', 'Fail'];
                    uniqueGrades.sort((a, b) => {
                        const aIndex = gradeOrder.indexOf(a.GradeName_EN);
                        const bIndex = gradeOrder.indexOf(b.GradeName_EN);
                        if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
                        if (aIndex !== -1) return -1;
                        if (bIndex !== -1) return 1;
                        return a.GradeName_EN.localeCompare(b.GradeName_EN);
                    });

                    uniqueGrades.forEach(function(grade) {
                        const displayName = lang === 'ar' ?
                            (grade.GradeName_AR || grade.GradeName_EN) :
                            grade.GradeName_EN;
                        gradeSelect.append(`<option value="${grade.GradeName_EN}" data-grade-id="${grade.Grade_ID}" data-ar="${grade.GradeName_AR}">${displayName}</option>`);
                    });

                    // Restore previous selection if it exists
                    if (currentValue) {
                        gradeSelect.val(currentValue);
                    }
                } else {
                    // Fallback to hardcoded options if data not loaded yet
                    const fallbackOptions = {
                        en: [
                            { value: 'Excellent', text: 'Excellent' },
                            { value: 'Very Good', text: 'Very Good' },
                            { value: 'Good', text: 'Good' },
                            { value: 'Pass', text: 'Pass' }
                        ],
                        ar: [
                            { value: 'Excellent', text: 'ممتاز' },
                            { value: 'Very Good', text: 'جيد جداً' },
                            { value: 'Good', text: 'جيد' },
                            { value: 'Pass', text: 'مقبول' }
                        ]
                    };

                    fallbackOptions[lang].forEach(function(option) {
                        gradeSelect.append(`<option value="${option.value}">${option.text}</option>`);
                    });
                }
            }

            function updateHearAboutUsDropdown(lang) {
                const hearAboutUsOptions = {
                    en: [
                        { value: '', text: 'How did you hear about us?' },
                        { value: 'Wuzzuf', text: 'Wuzzuf' },
                        { value: 'Facebook', text: 'Facebook' },
                        { value: 'Forasna', text: 'Forasna' },
                        { value: 'Recommendation', text: 'Recommendation' },
                        { value: 'Linkedin', text: 'Linkedin' }
                    ],
                    ar: [
                        { value: '', text: 'كيف سمعت عنا؟' },
                        { value: 'Wuzzuf', text: 'وظف' },
                        { value: 'Facebook', text: 'فيسبوك' },
                        { value: 'Forasna', text: 'فرصنا' },
                        { value: 'Recommendation', text: 'توصية' },
                        { value: 'Linkedin', text: 'لينكد إن' }
                    ]
                };
                updateDropdown('#hearAboutUs', hearAboutUsOptions[lang]);
            }

            function updateYesNoDropdowns(lang) {
                const yesNoOptions = {
                    en: [
                        { value: 'Yes', text: 'Yes' },
                        { value: 'No', text: 'No' }
                    ],
                    ar: [
                        { value: 'Yes', text: 'نعم' },
                        { value: 'No', text: 'لا' }
                    ]
                };

                // Update all Yes/No dropdowns
                const yesNoDropdowns = [
                    { selector: '#workAtShifa', placeholder: { en: 'Do you work at Shifa Hospital Before?', ar: 'هل عملت في مستشفى الشفاء من قبل؟' }},
                    { selector: '#chronicDiseases', placeholder: { en: 'Do you have any chronic diseases?', ar: 'هل تعاني من أي أمراض مزمنة؟' }},
                    { selector: '#relativeInHospital', placeholder: { en: 'Do you have Relatives in the Hospital?', ar: 'هل لديك أقارب في المستشفى؟' }}
                ];

                yesNoDropdowns.forEach(function(dropdown) {
                    const options = [
                        { value: '', text: dropdown.placeholder[lang] },
                        ...yesNoOptions[lang]
                    ];
                    updateDropdown(dropdown.selector, options);
                });
            }

            // Arabic Alert Messages and Notifications System
            function getAlertMessages(lang) {
                return {
                    en: {
                        // Error messages
                        fillRequiredFields: 'Please fill all required fields to continue',
                        validationError: 'Validation Error',
                        networkError: 'Network Error',
                        serverError: 'Server Error',
                        uploadError: 'Upload Error',

                        // Success messages
                        formSubmitted: 'Application Submitted Successfully',
                        dataSaved: 'Data Saved Successfully',
                        uploadSuccess: 'File Uploaded Successfully',

                        // Warning messages
                        unsavedChanges: 'You have unsaved changes. Are you sure you want to leave?',
                        confirmSubmit: 'Are you sure you want to submit the application?',
                        confirmReset: 'Are you sure you want to reset the form?',

                        // Loading messages
                        submitting: 'Submitting Application...',
                        loading: 'Loading...',
                        uploading: 'Uploading File...',
                        processing: 'Processing...',

                        // Button text
                        ok: 'OK',
                        cancel: 'Cancel',
                        yes: 'Yes',
                        no: 'No',
                        close: 'Close',
                        retry: 'Retry'
                    },
                    ar: {
                        // Error messages
                        fillRequiredFields: 'يرجى ملء جميع الحقول المطلوبة للمتابعة',
                        validationError: 'خطأ في التحقق',
                        networkError: 'خطأ في الشبكة',
                        serverError: 'خطأ في الخادم',
                        uploadError: 'خطأ في الرفع',

                        // Success messages
                        formSubmitted: 'تم إرسال الطلب بنجاح',
                        dataSaved: 'تم حفظ البيانات بنجاح',
                        uploadSuccess: 'تم رفع الملف بنجاح',

                        // Warning messages
                        unsavedChanges: 'لديك تغييرات غير محفوظة. هل أنت متأكد من أنك تريد المغادرة؟',
                        confirmSubmit: 'هل أنت متأكد من أنك تريد إرسال الطلب؟',
                        confirmReset: 'هل أنت متأكد من أنك تريد إعادة تعيين النموذج؟',

                        // Loading messages
                        submitting: 'جاري إرسال الطلب...',
                        loading: 'جاري التحميل...',
                        uploading: 'جاري رفع الملف...',
                        processing: 'جاري المعالجة...',

                        // Button text
                        ok: 'موافق',
                        cancel: 'إلغاء',
                        yes: 'نعم',
                        no: 'لا',
                        close: 'إغلاق',
                        retry: 'إعادة المحاولة'
                    }
                };
            }

            // Centralized alert functions with Arabic support
            function showErrorMessage(messageKey, customMessage = null) {
                const messages = getAlertMessages(currentLanguage);
                const message = customMessage || messages[currentLanguage][messageKey] || messageKey;
                const title = messages[currentLanguage].validationError;

                Swal.fire({
                    icon: 'error',
                    title: title,
                    text: message,
                    confirmButtonText: messages[currentLanguage].ok,
                    confirmButtonColor: '#d33',
                    customClass: {
                        popup: currentLanguage === 'ar' ? 'rtl-popup' : ''
                    }
                });
            }

            function showSuccessMessage(messageKey, customMessage = null) {
                const messages = getAlertMessages(currentLanguage);
                const message = customMessage || messages[currentLanguage][messageKey] || messageKey;

                Swal.fire({
                    icon: 'success',
                    title: message,
                    showConfirmButton: false,
                    timer: 3000,
                    customClass: {
                        popup: currentLanguage === 'ar' ? 'rtl-popup' : ''
                    }
                });
            }

            function showWarningMessage(messageKey, customMessage = null) {
                const messages = getAlertMessages(currentLanguage);
                const message = customMessage || messages[currentLanguage][messageKey] || messageKey;
                const title = messages[currentLanguage].validationError;

                return Swal.fire({
                    icon: 'warning',
                    title: title,
                    text: message,
                    showCancelButton: true,
                    confirmButtonText: messages[currentLanguage].yes,
                    cancelButtonText: messages[currentLanguage].no,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    customClass: {
                        popup: currentLanguage === 'ar' ? 'rtl-popup' : ''
                    }
                });
            }

            function showLoadingMessage(messageKey) {
                const messages = getAlertMessages(currentLanguage);
                const message = messages[currentLanguage][messageKey] || messageKey;

                Swal.fire({
                    title: message,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    },
                    customClass: {
                        popup: currentLanguage === 'ar' ? 'rtl-popup' : ''
                    }
                });
            }

            // Initialize form sections and progress indicator
            function initializeForm() {
                $("#prev").hide();
                $("section").not("section:nth-of-type(1)").hide();
                $("section:nth-of-type(1)").addClass('active');

                // Create SVG progress indicator
                var svgWidth = length * 150 + 24;
                $("#svg_wrap").html(
                    '<svg version="1.1" id="svg_form_time" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 ' +
                    svgWidth + ' 24" xml:space="preserve"></svg>'
                );

                function makeSVG(tag, attrs) {
                    var el = document.createElementNS("http://www.w3.org/2000/svg", tag);
                    for (var k in attrs) el.setAttribute(k, attrs[k]);
                    return el;
                }

                for (i = 0; i < length; i++) {
                    var positionX = 12 + i * 150;
                    var rect = makeSVG("rect", { x: positionX, y: 9, width: 150, height: 6 });
                    document.getElementById("svg_form_time").appendChild(rect);
                    var circle = makeSVG("circle", {
                        cx: positionX,
                        cy: 12,
                        r: 12,
                        width: positionX,
                        height: 6
                    });
                    document.getElementById("svg_form_time").appendChild(circle);
                }

                var circle = makeSVG("circle", {
                    cx: positionX + 150,
                    cy: 12,
                    r: 12,
                    width: positionX,
                    height: 6
                });
                document.getElementById("svg_form_time").appendChild(circle);

                $('#svg_form_time rect').css('fill', base_color);
                $('#svg_form_time circle').css('fill', base_color);
                $("circle:nth-of-type(1)").css("fill", active_color);
            }

            // Initialize Select2 dropdowns
            function initializeSelect2() {
                $('.form-control').each(function() {
                    if ($(this).is('select') && !$(this).prop('readonly')) {
                        // Skip specialization field - it will be initialized separately
                        if ($(this).attr('id') === 'specialization') {
                            return;
                        }

                        $(this).select2({
                            theme: 'bootstrap4',
                            placeholder: $(this).find('option:first').text(),
                            allowClear: false
                        });
                    }
                });

                // Initialize specialization field separately (starts disabled)
                initializeSpecializationSelect2();
            }

            // Initialize Specialization Select2 separately
            function initializeSpecializationSelect2() {
                const specializationSelect = $('#specialization');

                // Destroy existing Select2 if it exists
                if (specializationSelect.hasClass('select2-hidden-accessible')) {
                    specializationSelect.select2('destroy');
                }

                // Initialize with proper settings
                specializationSelect.select2({
                    theme: 'bootstrap4',
                    placeholder: 'First select Scientific Degree',
                    allowClear: false,
                    disabled: true
                });
            }

            // Setup navigation
            function setupNavigation() {
                $(".button").click(function () {
                    var id = $(this).attr("id");
                    if (id == "next") {
                        if (!validateCurrentSection()) {
                            // Error message is now handled inside validateCurrentSection()
                            return;
                        }

                        if (child < length) {
                            child++;
                            updateSectionVisibility();
                        }
                    } else if (id == "prev") {
                        if (child > 1) {
                            child--;
                            updateSectionVisibility();
                        }
                    }
                    updateButtonVisibility();
                });
            }

            // Update section visibility with animations
            function updateSectionVisibility() {
                $('section').removeClass('active').hide();
                var currentSection = $("section:nth-of-type(" + child + ")");
                currentSection.fadeIn(300).addClass('active');

                // Update progress indicator
                $("#svg_form_time rect").css("fill", base_color);
                $("#svg_form_time circle").css("fill", base_color);
                $("#svg_form_time rect:nth-of-type(-n+" + (child-1) + ")").css("fill", active_color);
                $("#svg_form_time circle:nth-of-type(-n+" + child + ")").css("fill", active_color);
            }

            // Update button visibility
            function updateButtonVisibility() {
                if (child === 1) {
                    $("#prev").hide();
                    $("#next").show();
                } else if (child === length) {
                    $("#prev").show();
                    $("#next").hide();
                } else {
                    $("#prev").show();
                    $("#next").show();
                }
            }

            // Get field-specific error messages
            function getFieldErrorMessages(lang) {
                return {
                    en: {
                        firstName: 'First name is required',
                        secondName: 'Second name is required',
                        thirdName: 'Third name is required',
                        phoneNumber: 'Valid phone number is required',
                        email: 'Valid email address is required',
                        nationalId: 'Valid ID number is required',
                        address: 'Address is required',
                        age: 'Age is required',
                        dateOfBirth: 'Date of birth is required',
                        gender: 'Gender selection is required',
                        religion: 'Religion selection is required',
                        socialStatus: 'Marital status is required',
                        city: 'City selection is required',
                        conscriptionStatus: 'Military status is required',
                        scientificDegree: 'Scientific degree is required',
                        specialization: 'Specialization is required',
                        university: 'University is required',
                        faculty: 'Faculty is required',
                        grade: 'Grade is required',
                        yearOfGraduation: 'Year of graduation is required',
                        currentEmployer: 'Current employer is required',
                        jobTitle: 'Job title is required',
                        fromDate: 'Start date is required',
                        toDate: 'End date is required',
                        currentSalary: 'Current salary is required',
                        reasonForLeaving: 'Reason for leaving is required',
                        hearAboutUs: 'Please select how you heard about us',
                        position: 'Position selection is required',
                        expectedSalary: 'Expected salary is required',
                        englishLevel: 'English level is required',
                        computerSkills: 'Computer skills level is required',
                        workAtShifa: 'Previous work at Shifa selection is required',
                        chronicDiseases: 'Chronic diseases selection is required',
                        relativeInHospital: 'Relatives in hospital selection is required'
                    },
                    ar: {
                        firstName: 'الاسم الأول مطلوب',
                        secondName: 'الاسم الثاني مطلوب',
                        thirdName: 'الاسم الثالث مطلوب',
                        phoneNumber: 'رقم هاتف صحيح مطلوب',
                        email: 'عنوان بريد إلكتروني صحيح مطلوب',
                        nationalId: 'رقم هوية صحيح مطلوب',
                        address: 'العنوان مطلوب',
                        age: 'العمر مطلوب',
                        dateOfBirth: 'تاريخ الميلاد مطلوب',
                        gender: 'اختيار الجنس مطلوب',
                        religion: 'اختيار الديانة مطلوب',
                        socialStatus: 'الحالة الاجتماعية مطلوبة',
                        city: 'اختيار المدينة مطلوب',
                        conscriptionStatus: 'الموقف من التجنيد مطلوب',
                        scientificDegree: 'الدرجة العلمية مطلوبة',
                        specialization: 'التخصص مطلوب',
                        university: 'الجامعة مطلوبة',
                        faculty: 'الكلية مطلوبة',
                        grade: 'التقدير مطلوب',
                        yearOfGraduation: 'سنة التخرج مطلوبة',
                        currentEmployer: 'جهة العمل الحالية مطلوبة',
                        jobTitle: 'المسمى الوظيفي مطلوب',
                        fromDate: 'تاريخ البداية مطلوب',
                        toDate: 'تاريخ النهاية مطلوب',
                        currentSalary: 'الراتب الحالي مطلوب',
                        reasonForLeaving: 'سبب ترك العمل مطلوب',
                        hearAboutUs: 'يرجى اختيار كيف سمعت عنا',
                        position: 'اختيار المنصب مطلوب',
                        expectedSalary: 'الراتب المتوقع مطلوب',
                        englishLevel: 'مستوى الإنجليزية مطلوب',
                        computerSkills: 'مستوى مهارات الحاسوب مطلوب',
                        workAtShifa: 'اختيار العمل السابق في الشفاء مطلوب',
                        chronicDiseases: 'اختيار الأمراض المزمنة مطلوب',
                        relativeInHospital: 'اختيار الأقارب في المستشفى مطلوب'
                    }
                };
            }

            // Validate current section with specific error messages
            function validateCurrentSection() {
                const currentSection = $(`section:nth-of-type(${child})`);
                let isValid = true;
                let missingFields = [];
                const fieldMessages = getFieldErrorMessages(currentLanguage);

                currentSection.find('input[required], select[required]').each(function() {
                    if ($(this).prop('readonly') || $(this).attr('id') === 'cvFile') return;

                    const fieldId = $(this).attr('id');
                    const fieldValue = $(this).val();
                    const hasValue = fieldValue && fieldValue !== '' && fieldValue !== null && fieldValue !== 'null';

                    if (!hasValue || $(this).hasClass('is-invalid')) {
                        isValid = false;
                        $(this).addClass('shake');
                        setTimeout(() => {
                            $(this).removeClass('shake');
                        }, 600);

                        // Add field-specific error message
                        const fieldMessage = fieldMessages[currentLanguage][fieldId];
                        if (fieldMessage) {
                            missingFields.push(fieldMessage);
                        }
                    }
                });

                // Show specific error message if validation fails
                if (!isValid && missingFields.length > 0) {
                    const errorMessage = missingFields.length === 1 ?
                        missingFields[0] :
                        (currentLanguage === 'ar' ?
                            'يرجى ملء الحقول التالية:\n• ' + missingFields.join('\n• ') :
                            'Please fill the following fields:\n• ' + missingFields.join('\n• ')
                        );

                    showErrorMessage('validationError', errorMessage);
                }

                return isValid;
            }

            // Load positions
            function loadPositions() {
                $.ajax({
                    url: '/api/positions',
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        const positionSelect = $('#position');
                        const selectText = currentLanguage === 'ar' ? 'اختر المنصب' : 'Select Position';
                        positionSelect.empty().append(`<option value="">${selectText}</option>`);

                        if (Array.isArray(data)) {
                            data.forEach(function(position, index) {
                                let posId = position.PositionId || position.HIMS_ID || (index + 1000);
                                positionSelect.append(`<option value="${posId}"
                                    data-department="${position.DepartmentId}"
                                    data-section="${position.SECTIONId}">
                                    ${position.PositionName}
                                </option>`);
                            });
                        }

                        positionSelect.trigger('change');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading positions:', error);
                        showErrorMessage('networkError', currentLanguage === 'ar' ? 'فشل في تحميل المناصب. يرجى تحديث الصفحة والمحاولة مرة أخرى.' : 'Failed to load positions. Please refresh the page and try again.');
                    }
                });

                $('#position').on('change', function() {
                    const selectedOption = $(this).find('option:selected');
                    const departmentId = selectedOption.data('department');
                    const sectionId = selectedOption.data('section');

                    $('#departmentId').val(departmentId || 1);
                    $('#sectionId').val(sectionId || 1);
                });
            }

            // Load Scientific Degrees
            function loadScientificDegrees() {
                const scientificDegreeSelect = $('#scientificDegree');

                // Show loading state
                const loadingText = currentLanguage === 'ar' ? 'جاري التحميل...' : 'Loading...';
                scientificDegreeSelect.html(`<option value="">${loadingText}</option>`);
                scientificDegreeSelect.prop('disabled', true);

                $.ajax({
                    url: '/api/scientific-degrees',
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        const selectText = currentLanguage === 'ar' ? 'اختر الدرجة العلمية' : 'Select Scientific Degree';
                        scientificDegreeSelect.empty().append(`<option value="" disabled selected>${selectText}</option>`);

                        if (Array.isArray(data) && data.length > 0) {
                            data.forEach(function(degree) {
                                const displayName = currentLanguage === 'ar' ?
                                    (degree.CategoryName_AR || degree.CategoryName_EN) :
                                    degree.CategoryName_EN;
                                scientificDegreeSelect.append(`<option value="${degree.Category_ID}">${displayName}</option>`);
                            });
                        } else {
                            const noDataText = currentLanguage === 'ar' ? 'لا توجد درجات علمية متاحة' : 'No degrees available';
                            scientificDegreeSelect.append(`<option value="" disabled>${noDataText}</option>`);
                        }

                        scientificDegreeSelect.prop('disabled', false);

                        // Reinitialize Select2 if it exists
                        if (scientificDegreeSelect.hasClass('select2-hidden-accessible')) {
                            scientificDegreeSelect.select2('destroy');
                        }
                        scientificDegreeSelect.select2({
                            theme: 'bootstrap4',
                            placeholder: selectText,
                            allowClear: false
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading scientific degrees:', error);
                        const errorText = currentLanguage === 'ar' ? 'خطأ في تحميل الدرجات العلمية' : 'Error loading degrees';
                        scientificDegreeSelect.empty().append(`<option value="" disabled>${errorText}</option>`);
                        scientificDegreeSelect.prop('disabled', false);
                        showErrorMessage('networkError', currentLanguage === 'ar' ? 'فشل في تحميل الدرجات العلمية. يرجى تحديث الصفحة والمحاولة مرة أخرى.' : 'Failed to load scientific degrees. Please refresh the page and try again.');
                    }
                });
            }

            // Load Specializations based on Scientific Degree
            function loadSpecializations(categoryId) {
                const specializationSelect = $('#specialization');

                // Destroy existing Select2 first
                if (specializationSelect.hasClass('select2-hidden-accessible')) {
                    specializationSelect.select2('destroy');
                }

                if (!categoryId) {
                    // Reset to disabled state
                    const placeholderText = currentLanguage === 'ar' ? 'اختر الدرجة العلمية أولاً' : 'First select Scientific Degree';
                    specializationSelect.empty().append(`<option value="" disabled selected>${placeholderText}</option>`);
                    specializationSelect.prop('disabled', true);

                    // Reinitialize Select2 in disabled state
                    specializationSelect.select2({
                        theme: 'bootstrap4',
                        placeholder: placeholderText,
                        allowClear: false,
                        disabled: true
                    });
                    return;
                }

                // Show loading state
                const loadingText = currentLanguage === 'ar' ? 'جاري تحميل التخصصات...' : 'Loading specializations...';
                specializationSelect.empty().append(`<option value="" disabled selected>${loadingText}</option>`);
                specializationSelect.prop('disabled', true);

                // Initialize Select2 for loading state
                specializationSelect.select2({
                    theme: 'bootstrap4',
                    placeholder: loadingText,
                    allowClear: false,
                    disabled: true
                });

                $.ajax({
                    url: `/api/specializations/${categoryId}`,
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        console.log('Specializations loaded:', data);

                        // Destroy Select2 before updating options
                        if (specializationSelect.hasClass('select2-hidden-accessible')) {
                            specializationSelect.select2('destroy');
                        }

                        // Clear and populate options
                        const selectText = currentLanguage === 'ar' ? 'اختر التخصص' : 'Select Specialization';
                        specializationSelect.empty().append(`<option value="" disabled selected>${selectText}</option>`);

                        if (Array.isArray(data) && data.length > 0) {
                            data.forEach(function(spec) {
                                const displayName = currentLanguage === 'ar' ?
                                    (spec.GradeName_AR || spec.GradeName_EN) :
                                    spec.GradeName_EN;
                                specializationSelect.append(`<option value="${spec.Grade_ID}">${displayName}</option>`);
                            });
                        } else {
                            const noDataText = currentLanguage === 'ar' ? 'لا توجد تخصصات متاحة لهذه الدرجة' : 'No specializations available for this degree';
                            specializationSelect.append(`<option value="" disabled>${noDataText}</option>`);
                        }

                        // Enable the field
                        specializationSelect.prop('disabled', false);

                        // Reinitialize Select2 with enabled state
                        specializationSelect.select2({
                            theme: 'bootstrap4',
                            placeholder: selectText,
                            allowClear: false,
                            disabled: false
                        });

                        console.log('Specialization dropdown reinitialized');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading specializations:', error);

                        // Destroy Select2 before updating
                        if (specializationSelect.hasClass('select2-hidden-accessible')) {
                            specializationSelect.select2('destroy');
                        }

                        const errorText = currentLanguage === 'ar' ? 'خطأ في تحميل التخصصات' : 'Error loading specializations';
                        specializationSelect.empty().append(`<option value="" disabled selected>${errorText}</option>`);
                        specializationSelect.prop('disabled', true);

                        // Reinitialize Select2 in error state
                        specializationSelect.select2({
                            theme: 'bootstrap4',
                            placeholder: errorText,
                            allowClear: false,
                            disabled: true
                        });

                        showErrorMessage('networkError', currentLanguage === 'ar' ? 'فشل في تحميل التخصصات. يرجى المحاولة مرة أخرى.' : 'Failed to load specializations. Please try again.');
                    }
                });
            }

            // Load Universities
            function loadUniversities() {
                const universitySelect = $('#university');

                // Show loading state
                const loadingText = currentLanguage === 'ar' ? 'جاري التحميل...' : 'Loading...';
                universitySelect.html(`<option value="">${loadingText}</option>`);
                universitySelect.prop('disabled', true);

                $.ajax({
                    url: '/api/universities',
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        const selectText = currentLanguage === 'ar' ? 'اختر الجامعة' : 'Select University';
                        universitySelect.empty().append(`<option value="" disabled selected>${selectText}</option>`);

                        if (Array.isArray(data) && data.length > 0) {
                            data.forEach(function(uni) {
                                const displayName = currentLanguage === 'ar' ?
                                    (uni.uniname_ar || uni.uniname_en) :
                                    uni.uniname_en;
                                universitySelect.append(`<option value="${uni.uni_id}">${displayName}</option>`);
                            });
                        } else {
                            const noDataText = currentLanguage === 'ar' ? 'لا توجد جامعات متاحة' : 'No universities available';
                            universitySelect.append(`<option value="" disabled>${noDataText}</option>`);
                        }

                        universitySelect.prop('disabled', false);

                        // Reinitialize Select2
                        if (universitySelect.hasClass('select2-hidden-accessible')) {
                            universitySelect.select2('destroy');
                        }
                        universitySelect.select2({
                            theme: 'bootstrap4',
                            placeholder: selectText,
                            allowClear: false
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading universities:', error);
                        const errorText = currentLanguage === 'ar' ? 'خطأ في تحميل الجامعات' : 'Error loading universities';
                        universitySelect.empty().append(`<option value="" disabled>${errorText}</option>`);
                        universitySelect.prop('disabled', false);
                        showErrorMessage('networkError', currentLanguage === 'ar' ? 'فشل في تحميل الجامعات. يرجى تحديث الصفحة والمحاولة مرة أخرى.' : 'Failed to load universities. Please refresh the page and try again.');
                    }
                });
            }

            // Load Grades from Qualifications table
            function loadGrades() {
                const gradeSelect = $('#grade');

                // Show loading state
                const loadingText = currentLanguage === 'ar' ? 'جاري التحميل...' : 'Loading...';
                gradeSelect.html(`<option value="">${loadingText}</option>`);
                gradeSelect.prop('disabled', true);

                $.ajax({
                    url: '/api/qualifications',
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        const selectText = currentLanguage === 'ar' ? 'اختر التقدير' : 'Select Grade';
                        gradeSelect.empty().append(`<option value="" disabled selected>${selectText}</option>`);

                        if (Array.isArray(data) && data.length > 0) {
                            // Get unique grades (since Qualifications table may have duplicates for different categories)
                            const uniqueGrades = [];
                            const seenGrades = new Set();

                            data.forEach(function(qual) {
                                if (qual.Grade_ID && qual.GradeName_EN && !seenGrades.has(qual.Grade_ID)) {
                                    seenGrades.add(qual.Grade_ID);
                                    uniqueGrades.push(qual);
                                }
                            });

                            // Sort grades by a logical order (Excellent, Very Good, Good, Pass, etc.)
                            const gradeOrder = ['Excellent', 'Very Good', 'Good', 'Pass', 'Fail'];
                            uniqueGrades.sort((a, b) => {
                                const aIndex = gradeOrder.indexOf(a.GradeName_EN);
                                const bIndex = gradeOrder.indexOf(b.GradeName_EN);
                                if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
                                if (aIndex !== -1) return -1;
                                if (bIndex !== -1) return 1;
                                return a.GradeName_EN.localeCompare(b.GradeName_EN);
                            });

                            uniqueGrades.forEach(function(grade) {
                                const displayName = currentLanguage === 'ar' ?
                                    (grade.GradeName_AR || grade.GradeName_EN) :
                                    grade.GradeName_EN;
                                gradeSelect.append(`<option value="${grade.GradeName_EN}" data-grade-id="${grade.Grade_ID}" data-ar="${grade.GradeName_AR}">${displayName}</option>`);
                            });
                        } else {
                            const noDataText = currentLanguage === 'ar' ? 'لا توجد تقديرات متاحة' : 'No grades available';
                            gradeSelect.append(`<option value="" disabled>${noDataText}</option>`);
                        }

                        gradeSelect.prop('disabled', false);

                        // Reinitialize Select2
                        if (gradeSelect.hasClass('select2-hidden-accessible')) {
                            gradeSelect.select2('destroy');
                        }
                        gradeSelect.select2({
                            theme: 'bootstrap4',
                            placeholder: selectText,
                            allowClear: false
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading grades:', error);
                        const errorText = currentLanguage === 'ar' ? 'خطأ في تحميل التقديرات' : 'Error loading grades';
                        gradeSelect.empty().append(`<option value="" disabled>${errorText}</option>`);
                        gradeSelect.prop('disabled', false);
                        showErrorMessage('networkError', currentLanguage === 'ar' ? 'فشل في تحميل التقديرات. يرجى تحديث الصفحة والمحاولة مرة أخرى.' : 'Failed to load grades. Please refresh the page and try again.');
                    }
                });
            }

            // Load Faculties
            function loadFaculties() {
                const facultySelect = $('#faculty');

                // Show loading state
                const loadingText = currentLanguage === 'ar' ? 'جاري التحميل...' : 'Loading...';
                facultySelect.html(`<option value="">${loadingText}</option>`);
                facultySelect.prop('disabled', true);

                $.ajax({
                    url: '/api/faculties',
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        const selectText = currentLanguage === 'ar' ? 'اختر الكلية' : 'Select Faculty';
                        facultySelect.empty().append(`<option value="" disabled selected>${selectText}</option>`);

                        if (Array.isArray(data) && data.length > 0) {
                            data.forEach(function(faculty) {
                                const displayName = currentLanguage === 'ar' ?
                                    (faculty.facname_ar || faculty.facname_en) :
                                    faculty.facname_en;
                                facultySelect.append(`<option value="${faculty.fac_id}">${displayName}</option>`);
                            });
                        } else {
                            const noDataText = currentLanguage === 'ar' ? 'لا توجد كليات متاحة' : 'No faculties available';
                            facultySelect.append(`<option value="" disabled>${noDataText}</option>`);
                        }

                        facultySelect.prop('disabled', false);

                        // Reinitialize Select2
                        if (facultySelect.hasClass('select2-hidden-accessible')) {
                            facultySelect.select2('destroy');
                        }
                        facultySelect.select2({
                            theme: 'bootstrap4',
                            placeholder: selectText,
                            allowClear: false
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading faculties:', error);
                        const errorText = currentLanguage === 'ar' ? 'خطأ في تحميل الكليات' : 'Error loading faculties';
                        facultySelect.empty().append(`<option value="" disabled>${errorText}</option>`);
                        facultySelect.prop('disabled', false);
                        showErrorMessage('networkError', currentLanguage === 'ar' ? 'فشل في تحميل الكليات. يرجى تحديث الصفحة والمحاولة مرة أخرى.' : 'Failed to load faculties. Please refresh the page and try again.');
                    }
                });
            }

            // Setup cascading dropdowns
            function setupCascadingDropdowns() {
                // Scientific Degree change handler
                $('#scientificDegree').on('change select2:select', function() {
                    const categoryId = $(this).val();
                    console.log('Scientific Degree changed:', categoryId);

                    // Load specializations based on selected scientific degree
                    if (categoryId && categoryId !== '') {
                        loadSpecializations(categoryId);
                    } else {
                        loadSpecializations(null);
                    }
                });

                // Reset specialization when scientific degree is cleared
                $('#scientificDegree').on('select2:unselect select2:clear', function() {
                    console.log('Scientific Degree cleared');
                    loadSpecializations(null);
                });

                // Debug: Log when specialization is clicked
                $(document).on('click', '.select2-container--bootstrap4', function() {
                    const targetId = $(this).prev('select').attr('id');
                    if (targetId === 'specialization') {
                        console.log('Specialization dropdown clicked');
                    }
                });
            }

            // Setup validation
            function setupValidation() {
                // Real-time validation for all form fields
                $('.form-control, .name-field input').on('blur input', function() {
                    validateField($(this));
                });

                // Handle Select2 change events
                $('.form-control').on('select2:select select2:unselect change', function() {
                    validateField($(this));
                });

                // Handle ID type change
                $('#idType').change(function() {
                    toggleIdFields($(this).val() === 'passport');
                });

                // Handle file input display
                $('.custom-file-input').on('change', function() {
                    var fileName = $(this).val().split('\\').pop();
                    const defaultText = currentLanguage === 'ar' ? 'اختر ملف' : 'Choose file';
                    $(this).next('.custom-file-label').html(fileName ? fileName : defaultText);
                });

                // National ID auto-fill functionality
                $('#nationalId').on('input', function() {
                    const idType = $('#idType').val();
                    const idValue = $(this).val();

                    if (idType === 'national' && idValue.length === 14) {
                        autoFillFromNationalId(idValue);
                    }
                });

                // Numeric input restrictions
                $('#phoneNumber, #currentSalary, #expectedSalary, #yearOfGraduation').on('input', function() {
                    this.value = this.value.replace(/[^0-9.]/g, '');
                });
            }

            // Validate individual field
            function validateField(field) {
                const id = field.attr('id');
                const rawValue = field.val();
                const value = rawValue ? rawValue.toString().trim() : '';

                field.removeClass('is-valid is-invalid shake pulse');

                // Skip optional empty fields
                if (!field.prop('required') && value === '') {
                    return true;
                }

                let isValid = true;

                switch(id) {
                    case 'firstName':
                    case 'secondName':
                    case 'thirdName':
                        isValid = value !== '' && /^([\u0600-\u06FF]+|[A-Za-z ]+)$/.test(value);
                        break;
                    case 'phoneNumber':
                        isValid = /^[0-9]{11}$/.test(value);
                        break;
                    case 'email':
                        if (value) {
                            isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                        }
                        break;
                    case 'nationalId':
                        const idType = $('#idType').val();
                        if (idType === 'national') {
                            isValid = /^\d{14}$/.test(value);
                        } else {
                            isValid = value.length >= 6;
                        }
                        break;
                    case 'address':
                        isValid = value.length >= 10;
                        break;
                    case 'yearOfGraduation':
                        const year = parseInt(value);
                        const currentYear = new Date().getFullYear();
                        isValid = year >= 1950 && year <= currentYear;
                        break;
                    case 'currentSalary':
                    case 'expectedSalary':
                        isValid = /^\d+(\.\d{0,2})?$/.test(value) && parseFloat(value) > 0;
                        break;
                    default:
                        if (field.is('select')) {
                            isValid = value !== '' && value !== null && value !== 'null';
                        } else {
                            isValid = value !== '';
                        }
                }

                if (isValid) {
                    field.addClass('is-valid');
                } else {
                    field.addClass('is-invalid');
                }

                return isValid;
            }

            // Toggle ID fields based on type
            function toggleIdFields(isPassport) {
                if (isPassport) {
                    $('#idLabel').html('<i class="fas fa-id-card"></i> Passport Number');
                    $('#nationalId').attr('pattern', '[A-Za-z0-9]{6,20}');
                    $('#idFeedback').text('Please enter a valid passport number');
                    $('#idTooltip').text('Passport numbers typically contain 6-20 alphanumeric characters.');
                    $('#idHelper').text('Enter your passport number with no spaces');
                } else {
                    $('#idLabel').html('<i class="fas fa-id-card"></i> National ID');
                    $('#nationalId').attr('pattern', '[0-9]{14}');
                    $('#idFeedback').text('Please enter a valid 14-digit National ID');
                    $('#idTooltip').text('Egyptian National ID should be 14 digits. Passport numbers vary by country.');
                    $('#idHelper').text('For Egyptian citizens: 14 digits without spaces');
                }

                validateField($('#nationalId'));
            }

            // Auto-fill from National ID
            function autoFillFromNationalId(nationalId) {
                try {
                    // Extract birth date from National ID
                    const century = nationalId.charAt(0) === '2' ? '19' : '20';
                    const year = century + nationalId.substring(1, 3);
                    const month = nationalId.substring(3, 5);
                    const day = nationalId.substring(5, 7);
                    const birthDate = `${day}/${month}/${year}`;

                    // Calculate age
                    const today = new Date();
                    const birth = new Date(year, month - 1, day);
                    let age = today.getFullYear() - birth.getFullYear();
                    const monthDiff = today.getMonth() - birth.getMonth();
                    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                        age--;
                    }

                    // Extract gender
                    const genderDigit = parseInt(nationalId.charAt(12));
                    const gender = genderDigit % 2 === 0 ? 'Female' : 'Male';

                    // Extract governorate (simplified mapping)
                    const govCode = nationalId.substring(7, 9);
                    const governorates = {
                        '01': 'Cairo', '02': 'Alexandria', '03': 'Port Said', '04': 'Suez',
                        '11': 'Damietta', '12': 'Dakahlia', '13': 'Sharqia', '14': 'Qalyubia',
                        '15': 'Kafr El Sheikh', '16': 'Gharbia', '17': 'Monufia', '18': 'Beheira',
                        '21': 'Ismailia', '22': 'Giza', '23': 'Beni Suef', '24': 'Faiyum',
                        '25': 'Minya', '26': 'Asyut', '27': 'Sohag', '28': 'Qena',
                        '29': 'Aswan', '31': 'Luxor', '32': 'Red Sea', '33': 'New Valley',
                        '34': 'Matrouh', '35': 'North Sinai', '36': 'South Sinai'
                    };

                    // Fill the fields
                    $('#age').val(age);
                    $('#dateOfBirth').val(birthDate);
                    $('#gender').val(gender);
                    $('#governorate').val(governorates[govCode] || 'Unknown');

                } catch (error) {
                    console.error('Error parsing National ID:', error);
                }
            }

            // Form submission
            $('#liveApplicationForm').on('submit', function(e) {
                e.preventDefault();

                if (!validateForm()) {
                    showErrorMessage('fillRequiredFields');
                    return false;
                }

                submitFormWithRetry(new FormData(this), 1, 3);
            });

            // Validate entire form
            function validateForm() {
                let isValid = true;

                $('.form-control, .name-field input').each(function() {
                    if ($(this).prop('required') && !$(this).prop('readonly')) {
                        try {
                            if (!validateField($(this))) {
                                isValid = false;
                            }
                        } catch (error) {
                            console.error('Validation error for field:', $(this).attr('id'), error);
                            isValid = false;
                        }
                    }
                });

                // Validate CV file size if uploaded
                const fileInput = $('#cvFile')[0];
                if (fileInput && fileInput.files && fileInput.files.length > 0) {
                    const fileSize = fileInput.files[0].size / 1024 / 1024; // Convert to MB
                    if (fileSize > 5) {
                        showErrorMessage('uploadError', currentLanguage === 'ar' ? 'يجب أن يكون حجم ملف السيرة الذاتية أقل من 5 ميجابايت' : 'CV file size must be less than 5MB');
                        isValid = false;
                    }
                }

                return isValid;
            }

            // Handle field mappings and ID lookups
            async function handleFieldMappingsAndIds(formData) {
                console.log('🔄 Processing field mappings and ID lookups...');

                try {
                    // Scientific Degree mapping
                    const scientificDegreeOption = $('#scientificDegree').find('option:selected');
                    if (scientificDegreeOption.length > 0 && scientificDegreeOption.val()) {
                        const categoryId = scientificDegreeOption.val();
                        const categoryNameEN = scientificDegreeOption.text().trim();

                        // Find Arabic name from data
                        const degreeData = scientificDegreesData.find(deg => deg.Category_ID == categoryId);
                        const categoryNameAR = degreeData ? degreeData.CategoryName_AR : categoryNameEN;

                        formData.set('CategoryName_AR', categoryNameAR);
                        formData.set('Category_ID', categoryId); // Ensure ID is set

                        console.log(`✅ Scientific Degree: ${categoryNameEN} (ID: ${categoryId}, AR: ${categoryNameAR})`);
                    }

                    // Grade mapping
                    const gradeOption = $('#grade').find('option:selected');
                    if (gradeOption.length > 0 && gradeOption.val()) {
                        const gradeValue = gradeOption.val(); // This is the GradeName_EN
                        const categoryId = $('#scientificDegree').val();

                        // Find Grade_ID and GradeName_AR from database
                        const gradeId = findGradeIdByName(gradeValue, categoryId);
                        const gradeNameAR = findGradeArNameByEnName(gradeValue, categoryId);

                        if (gradeId) {
                            formData.set('Grade_ID', gradeId);
                            console.log(`✅ Grade: ${gradeValue} (ID: ${gradeId})`);
                        }

                        // Set the actual Arabic name from database
                        formData.set('GradeName_AR', gradeNameAR);
                        console.log(`✅ Grade Arabic: ${gradeNameAR} (from database)`);
                    }

                    // University mapping
                    const universityOption = $('#university').find('option:selected');
                    if (universityOption.length > 0 && universityOption.val()) {
                        const universityId = universityOption.val();
                        const universityNameEN = universityOption.text().trim();

                        // Find Arabic name from data
                        const uniData = universitiesData.find(uni => uni.uni_id == universityId);
                        const uninameAR = uniData ? uniData.uniname_ar : universityNameEN;

                        formData.set('uniname_ar', uninameAR);
                        formData.set('UID', universityId); // Set University ID

                        console.log(`✅ University: ${universityNameEN} (ID: ${universityId}, AR: ${uninameAR})`);
                    }

                    // Faculty mapping
                    const facultyOption = $('#faculty').find('option:selected');
                    if (facultyOption.length > 0 && facultyOption.val()) {
                        const facultyId = facultyOption.val();
                        const facultyNameEN = facultyOption.text().trim();

                        // Find Arabic name from data
                        const facData = facultiesData.find(fac => fac.fac_id == facultyId);
                        const facultyNameAR = facData ? facData.facname_ar : facultyNameEN;

                        formData.set('Faculty', facultyNameEN); // Keep display name for the field we fixed
                        formData.set('FID', facultyId); // Set Faculty ID for database relationship

                        console.log(`✅ Faculty: ${facultyNameEN} (ID: ${facultyId}, AR: ${facultyNameAR})`);
                    }

                    console.log('🎉 Field mappings and ID lookups completed successfully');

                } catch (error) {
                    console.error('❌ Error in field mappings and ID lookups:', error);
                    // Continue with submission even if mapping fails
                }
            }

            // Submit form with retry logic
            async function submitFormWithRetry(formData, attempt, maxAttempts) {
                $('#submitBtn').addClass('is-loading').prop('disabled', true);

                // Create a complete name from first, second, and third names
                const fullName = $('#firstName').val().trim() + ' ' +
                               $('#secondName').val().trim() + ' ' +
                               $('#thirdName').val().trim();
                formData.set('Name', fullName);

                // Get the selected position data
                const selectedOption = $('#position').find('option:selected');
                const positionName = selectedOption.text().trim();

                // Set position name for Sub_Position
                if (positionName && positionName !== "Select Position") {
                    formData.set('Sub_Position', positionName);
                } else {
                    formData.set('Sub_Position', "Live Applicant");
                }

                // Set other position-related fields
                formData.set('Main_Position', $('#departmentId').val() || 1);
                formData.set('SECTIONId', $('#sectionId').val() || 1);

                // Force the application status to Live
                formData.set('Application_Status', 'Live');
                formData.set('live_application', 'true');
                formData.set('Status', 'Live');

                // Handle passport vs national ID correctly
                const idType = $('#idType').val();
                if (idType === 'passport') {
                    formData.set('Passport_Number', $('#nationalId').val().trim());
                    formData.set('National_ID', '29912010000000'); // Dummy National ID
                }

                // Handle empty email to satisfy database constraints
                const emailValue = $('#email').val().trim();
                if (!emailValue) {
                    formData.set('Email', '<EMAIL>');
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
                    formData.set('Email', '<EMAIL>');
                }

                // Handle Arabic field mappings and ID mappings for bilingual support
                await handleFieldMappingsAndIds(formData);

                // Debug: Log the form data being sent
                console.log('FORM SUBMISSION DATA:');
                for (let pair of formData.entries()) {
                    console.log(`${pair[0]}: ${pair[1]}`);
                }

                $.ajax({
                    url: '/api/save-candidate',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-Live-Form': 'true',
                        'X-Application-Status': 'Live'
                    },
                    success: function(response) {
                        $('#submitBtn').removeClass('is-loading').prop('disabled', false);

                        if (response.success) {
                            console.log('SUCCESS RESPONSE:', response);

                            // If CV was uploaded, handle that separately
                            const fileInput = $('#cvFile')[0];
                            if (fileInput.files.length > 0) {
                                uploadCV(response.candidateId, fileInput.files[0]);
                            } else {
                                showSuccessMessage('Application submitted successfully!');
                                resetForm();
                            }
                        } else {
                            showErrorMessage('Failed to submit application: ' + (response.error || 'Unknown error'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error details:', xhr.responseText);

                        if (attempt < maxAttempts) {
                            // Retry submission
                            setTimeout(function() {
                                submitFormWithRetry(formData, attempt + 1, maxAttempts);
                            }, 2000);
                        } else {
                            $('#submitBtn').removeClass('is-loading').prop('disabled', false);
                            console.error('Error submitting form:', xhr.responseText);
                            showErrorMessage('Failed to submit application. Please try again later.');
                        }
                    }
                });
            }

            // Upload CV
            function uploadCV(candidateId, file) {
                const cvFormData = new FormData();
                cvFormData.append('cvFile', file);
                cvFormData.append('candidateId', candidateId);
                cvFormData.append('departmentId', '1');
                cvFormData.append('positionId', $('#position').val());
                cvFormData.append('candidateName', $('#firstName').val().trim() + ' ' +
                                                $('#secondName').val().trim() + ' ' +
                                                $('#thirdName').val().trim());

                $.ajax({
                    url: '/upload-cv',
                    method: 'POST',
                    data: cvFormData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            showSuccessMessage('formSubmitted');
                            resetForm();
                        } else {
                            showWarningMessage('uploadError', currentLanguage === 'ar' ? 'تم إرسال طلبك، لكن فشل رفع السيرة الذاتية. يمكنك تحديث سيرتك الذاتية لاحقاً.' : 'Your application was submitted, but CV upload failed. You can update your CV later.');
                            resetForm();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error uploading CV:', xhr.responseText);
                        showWarningMessage('uploadError', currentLanguage === 'ar' ? 'تم إرسال طلبك، لكن فشل رفع السيرة الذاتية. يمكنك تحديث سيرتك الذاتية لاحقاً.' : 'Your application was submitted, but CV upload failed. You can update your CV later.');
                        resetForm();
                    }
                });
            }

            // Reset form
            function resetForm() {
                $('#liveApplicationForm')[0].reset();
                $('#position').val('').trigger('change');
                $('#scientificDegree').val('').trigger('change');
                $('#specialization').val('').trigger('change');
                $('#university').val('').trigger('change');
                $('#faculty').val('').trigger('change');
                $('#grade').val('').trigger('change');
                $('#religion').val('').trigger('change');
                $('#socialStatus').val('').trigger('change');
                $('#city').val('').trigger('change');
                $('#conscriptionStatus').val('').trigger('change');
                $('#englishLevel').val('').trigger('change');
                $('#computerSkills').val('').trigger('change');
                $('#workAtShifa').val('').trigger('change');
                $('#chronicDiseases').val('').trigger('change');
                $('#relativeInHospital').val('').trigger('change');
                $('#hearAboutUs').val('').trigger('change');
                $('#idType').val('national').trigger('change');
                $('.custom-file-label').text('Choose file');
                $('.is-valid').removeClass('is-valid');
                $('.is-invalid').removeClass('is-invalid');

                // Clear auto-filled readonly fields
                $('#age').val('');
                $('#dateOfBirth').val('');
                $('#gender').val('');
                $('#governorate').val('');

                // Reset specialization to disabled state
                loadSpecializations(null);

                // Reset to first step
                child = 1;
                updateSectionVisibility();
                updateButtonVisibility();

                // Update file upload text based on current language
                const fileText = currentLanguage === 'ar' ? 'اختر ملف' : 'Choose file';
                $('.custom-file-label').text(fileText);
            }
        });
    </script>
</body>
</html>